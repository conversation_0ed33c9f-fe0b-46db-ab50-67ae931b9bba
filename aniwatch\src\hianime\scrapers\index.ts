import { getHomePage } from "./homePage.js";
import { getAZList } from "./animeAZList.js";
import { getGenreAnime } from "./animeGenre.js";
import { getAnimeQtipInfo } from "./animeQtip.js";
import { getAnimeEpisodes } from "./animeEpisodes.js";
import { getAnimeCategory } from "./animeCategory.js";
import { getProducerAnimes } from "./animeProducer.js";
import { getEpisodeServers } from "./episodeServers.js";
import { getAnimeAboutInfo } from "./animeAboutInfo.js";
import { getAnimeSearchResults } from "./animeSearch.js";
import { getAnimeEpisodeSources } from "./animeEpisodeSrcs.js";
import { getAnimeSearchSuggestion } from "./animeSearchSuggestion.js";
import {
    getEstimatedSchedule,
    getNextEpisodeSchedule,
} from "./estimatedSchedule.js";

export {
    getAZList,
    getHomePage,
    getGenreAnime,
    getAnimeQtipInfo,
    getAnimeEpisodes,
    getAnimeCategory,
    getEpisodeServers,
    getProducerAnimes,
    getAnimeAboutInfo,
    getEstimatedSchedule,
    getAnimeSearchResults,
    getNextEpisodeSchedule,
    getAnimeEpisodeSources,
    getAnimeSearchSuggestion,
};
