# MCP Anime Episode Sources Tools

This document describes the MCP (Model Context Protocol) tools for getting anime episode streaming sources from hianime.sx (formerly aniwatch.to).

## Available Tools

### 1. `get_anime_episode_sources`

Gets streaming sources for a specific anime episode.

**Parameters:**
- `episode_id` (required): The episode identifier (e.g., "attack-on-titan-112?ep=1")
- `server` (optional): Server to use - "vidstreaming", "vidcloud", "streamsb", "streamtape" (default: "vidstreaming")
- `category` (optional): Episode category - "sub", "dub", "raw" (default: "sub")

**Returns:**
```json
{
  "success": true,
  "data": {
    "headers": {
      "Referer": "https://hianime.sx/"
    },
    "sources": [
      {
        "url": "https://megacloud.blog/embed-2/v2/e-1/...",
        "quality": "auto",
        "type": "mp4"
      }
    ],
    "subtitles": [],
    "intro": null,
    "download": "https://...",
    "embedURL": "https://...",
    "anilistID": 16498,
    "malID": 16498
  },
  "server": "vidstreaming",
  "category": "sub",
  "episodeId": "attack-on-titan-112?ep=1"
}
```

### 2. `get_available_servers`

Gets the list of available streaming servers and categories.

**Parameters:** None

**Returns:**
```json
{
  "success": true,
  "servers": [
    {
      "id": "vidstreaming",
      "name": "VidStreaming",
      "description": "Primary streaming server"
    },
    {
      "id": "vidcloud",
      "name": "VidCloud",
      "description": "Alternative streaming server"
    },
    {
      "id": "streamsb",
      "name": "StreamSB",
      "description": "StreamSB server"
    },
    {
      "id": "streamtape",
      "name": "StreamTape",
      "description": "StreamTape server"
    }
  ],
  "categories": ["sub", "dub", "raw"]
}
```

## Usage Examples

### Getting Episode Sources

```python
# Get sources for Attack on Titan episode 1 with vidstreaming server
result = await get_anime_episode_sources(
    ctx=context,
    episode_id="attack-on-titan-112?ep=1",
    server="vidstreaming",
    category="sub"
)

if result["success"]:
    sources = result["data"]["sources"]
    for source in sources:
        print(f"Quality: {source['quality']}, URL: {source['url']}")
```

### Getting Available Servers

```python
# Get list of available servers
servers_info = await get_available_servers(ctx=context)

if servers_info["success"]:
    for server in servers_info["servers"]:
        print(f"{server['name']}: {server['description']}")
```

## Server Status

Based on testing with hianime.sx:

✅ **Working Servers:**
- `vidstreaming` - Primary server, most reliable
- `vidcloud` - Alternative server, good backup

⚠️ **Limited Availability:**
- `streamsb` - Not available for all episodes
- `streamtape` - Not available for all episodes

## Error Handling

The tools return structured error responses:

```json
{
  "success": false,
  "error": "Error message",
  "errorType": "AnimeEpisodeSourcesError",
  "statusCode": 500,
  "server": "vidstreaming",
  "category": "sub",
  "episodeId": "invalid-episode"
}
```

Common error types:
- `ValueError` - Invalid parameters (server, category, missing episode_id)
- `AnimeEpisodeSourcesError` - Scraping-specific errors
- `UnexpectedError` - General exceptions

## Implementation Details

### Architecture

The MCP tools are built on top of a Python scraper that:

1. **Fetches episode servers** from hianime.sx AJAX endpoints
2. **Extracts streaming URLs** using specialized extractors for each server type
3. **Handles different video sources** (MegaCloud, VidCloud, etc.)
4. **Provides metadata** including AniList/MAL IDs

### Key Components

- `scrapers/animeEpisodeSources.py` - Main scraper logic
- `scrapers/extractors/` - Server-specific URL extractors
- `scrapers/client.py` - HTTP client with proper headers
- `scrapers/constants.py` - Base URLs and configuration

### Dependencies

- `requests` - HTTP requests
- `beautifulsoup4` - HTML parsing
- `mcp` - Model Context Protocol framework

## Testing

Run the test scripts to verify functionality:

```bash
# Test the main episode sources tool
python test_mcp_tool.py

# Test the available servers tool
python test_servers_tool.py

# Test with live data
python test_episode_sources_live.py
```

## Notes

- The scraper works with the current hianime.sx domain
- Some servers may not be available for all episodes
- The site occasionally changes domains, requiring updates to `constants.py`
- Rate limiting is handled automatically by the HTTP client
