name: "Run Tests"

on:
  push:
    branches: ["main"]
  pull_request:
    branches: ["main"]

jobs:
  run_tests:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup NodeJS
        uses: actions/setup-node@v4
        with:
          node-version: "22.x"

      - name: Install dependencies
        run: npm i

      - name: Run all tests
        run: npm test
