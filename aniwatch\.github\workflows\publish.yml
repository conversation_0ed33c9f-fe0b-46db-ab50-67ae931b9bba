name: "Publish Package"
on:
  release:
    types: [published]

concurrency: ${{ github.workflow }}-${{ github.ref }}

env:
  HUSKY: 0

jobs:
  publish_package:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9

      - name: Setup NodeJS
        uses: actions/setup-node@v4
        with:
          cache: pnpm
          node-version: 20
          registry-url: https://registry.npmjs.org/

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Lint and build
        run: pnpm run ci

      - name: Publish package
        run: npm publish
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}

      # - name: Setup .npmrc file to publish to GitHub Packages
      #   uses: actions/setup-node@v4
      #   with:
      #     cache: pnpm
      #     node-version: 20
      #     registry-url: https://npm.pkg.github.com
      #     scope: "@${{ github.repository_owner }}"
      # - run: npm run addscope
      # - name: Publish to GitHub Packages
      #   run: npm publish
      #   env:
      #     NODE_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
