name: "Create Release"
on:
  push:
    branches: ["main"]

jobs:
  changelog:
    runs-on: ubuntu-latest

    steps:
      - name: Checkouts repo
        uses: actions/checkout@v4

      - name: Conventional changelog action
        id: changelog
        uses: TriPSs/conventional-changelog-action@v5
        with:
          skip-on-empty: false
          git-user-name: "github-actions[bot]"
          git-user-email: "github-actions[bot]@users.noreply.github.com"
          pre-commit: ./scripts/format-package-json.js
          github-token: ${{ secrets.GITHUB_TOKEN }}

      - name: Create release
        if: ${{ steps.changelog.outputs.skipped == 'false' }}
        uses: ncipollo/release-action@v1
        with:
          generateReleaseNotes: true
          token: ${{ secrets.CHANGELOG_SECRET }}
          tag: ${{ steps.changelog.outputs.tag }}
          name: ${{ steps.changelog.outputs.tag }}
          body: ${{ steps.changelog.outputs.clean_changelog }}
