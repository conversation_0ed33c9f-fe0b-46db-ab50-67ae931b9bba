#!/usr/bin/env python3
"""
Final test of the anime episode sources implementation
"""

import asyncio
import logging
from scrapers.animeEpisodeSources import get_anime_episode_sources, AnimeEpisodeSourcesError

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_episode_sources_comprehensive():
    """Comprehensive test of the episode sources functionality"""
    
    print("🎬 Anime Episode Sources - Final Implementation Test")
    print("=" * 70)
    
    test_cases = [
        {
            "name": "Attack on Titan Episode",
            "episode_id": "attack-on-titan-112?ep=3304",
            "server": "hd-1",
            "category": "sub",
            "expected": "Should return embed URL with proper metadata"
        },
        {
            "name": "The Children of Shiunji Family Episode (Your Example)",
            "episode_id": "the-children-of-shiunji-family-19556?ep=136211", 
            "server": "hd-1",
            "category": "sub",
            "expected": "Should return embed URL for the specific episode you mentioned"
        },
        {
            "name": "Alternative Server Test",
            "episode_id": "attack-on-titan-112?ep=3304",
            "server": "hd-2",
            "category": "sub", 
            "expected": "Should try different server"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 Test {i}: {test_case['name']}")
        print("-" * 50)
        print(f"Episode ID: {test_case['episode_id']}")
        print(f"Server: {test_case['server']}")
        print(f"Category: {test_case['category']}")
        print(f"Expected: {test_case['expected']}")
        print()
        
        try:
            result = get_anime_episode_sources(
                episode_id=test_case['episode_id'],
                server=test_case['server'],
                category=test_case['category']
            )
            
            if result.get("success"):
                data = result.get("data", {})
                sources = data.get("sources", [])
                headers = data.get("headers", {})
                
                print(f"✅ SUCCESS")
                print(f"📊 Found {len(sources)} source(s)")
                
                for j, source in enumerate(sources, 1):
                    url = source.get('url', 'N/A')
                    quality = source.get('quality', 'N/A')
                    source_type = source.get('type', 'unknown')
                    
                    print(f"   Source {j}:")
                    print(f"     Type: {source_type}")
                    print(f"     Quality: {quality}")
                    print(f"     URL: {url[:80]}{'...' if len(url) > 80 else ''}")
                    
                    # Analyze the URL type
                    if url.endswith('.m3u8'):
                        print(f"     🎯 Direct HLS stream found!")
                    elif url.endswith('.mp4'):
                        print(f"     🎯 Direct MP4 video found!")
                    elif 'embed' in url or 'megacloud' in url:
                        print(f"     📺 Embed URL (requires additional processing)")
                    else:
                        print(f"     ❓ Unknown URL type")
                
                print(f"🔗 Headers: {len(headers)} header(s)")
                for key, value in headers.items():
                    print(f"   {key}: {value}")
                
                # Show metadata if available
                if data.get("anilistID"):
                    print(f"📺 AniList ID: {data['anilistID']}")
                if data.get("malID"):
                    print(f"📺 MAL ID: {data['malID']}")
                if data.get("embedURL"):
                    print(f"🔗 Embed URL: {data['embedURL'][:60]}...")
                    
            else:
                print(f"❌ FAILED")
                error = result.get('error', 'Unknown error')
                error_type = result.get('error_type', 'Unknown')
                print(f"Error: {error}")
                print(f"Error Type: {error_type}")
                
        except AnimeEpisodeSourcesError as e:
            print(f"❌ ANIME EPISODE SOURCES ERROR")
            print(f"Message: {e.message}")
            print(f"Function: {e.function_name}")
            print(f"Status Code: {e.status_code}")
            
        except Exception as e:
            print(f"❌ UNEXPECTED ERROR")
            print(f"Error: {str(e)}")
            print(f"Type: {type(e).__name__}")
        
        print("=" * 50)

def test_mcp_tools():
    """Test the MCP tools integration"""
    print(f"\n🔧 MCP Tools Integration Test")
    print("-" * 50)
    
    try:
        from main import get_available_servers
        
        # Create a mock context
        class MockContext:
            pass
        
        async def test_servers():
            ctx = MockContext()
            result = await get_available_servers(ctx)
            
            if result.get("success"):
                servers = result.get("servers", [])
                categories = result.get("categories", [])
                
                print(f"✅ Available Servers Tool Working")
                print(f"📊 Found {len(servers)} servers and {len(categories)} categories")
                
                print("\nServers:")
                for server in servers:
                    print(f"   - {server['id']}: {server['name']}")
                
                print("\nCategories:")
                for category in categories:
                    print(f"   - {category['id']}: {category['name']}")
            else:
                print(f"❌ Available Servers Tool Failed: {result.get('error')}")
        
        asyncio.run(test_servers())
        
    except Exception as e:
        print(f"❌ MCP Tools Test Failed: {str(e)}")

def show_solution_summary():
    """Show summary of the implementation and solutions"""
    print(f"\n📋 Implementation Summary")
    print("=" * 70)
    
    print("✅ WHAT WORKS:")
    print("   - Proper MCP tool integration")
    print("   - Server and category validation")
    print("   - Episode ID format handling")
    print("   - Error handling and logging")
    print("   - Metadata extraction (AniList/MAL IDs)")
    print("   - Embed URL extraction with proper headers")
    print("   - Consistent API structure matching TypeScript version")
    
    print("\n⚠️  CURRENT LIMITATIONS:")
    print("   - Returns embed URLs instead of direct video URLs")
    print("   - Cannot decrypt MegaCloud's complex WASM-based encryption")
    print("   - Limited anti-bot protection bypass")
    
    print("\n🔧 SOLUTIONS FOR PRODUCTION:")
    print("   1. Use TypeScript aniwatch-api as microservice (RECOMMENDED)")
    print("   2. Browser automation with Selenium/Playwright")
    print("   3. Hybrid Python + Node.js approach")
    print("   4. Use embed URLs directly in compatible players")
    print("   5. Try alternative servers (hd-2, streamsb, streamtape)")
    
    print("\n📖 DOCUMENTATION:")
    print("   - ANIME_EPISODE_SOURCES_README.md - Complete API documentation")
    print("   - MEGACLOUD_SOLUTION_GUIDE.md - Practical solutions guide")
    
    print("\n🎯 NEXT STEPS:")
    print("   - Choose a solution approach based on your needs")
    print("   - Consider running aniwatch-api alongside this Python implementation")
    print("   - Test with different servers to find the most reliable ones")

if __name__ == "__main__":
    try:
        asyncio.run(test_episode_sources_comprehensive())
        test_mcp_tools()
        show_solution_summary()
        
    except KeyboardInterrupt:
        print("\n\n⚠️  Test interrupted by user")
    except Exception as e:
        print(f"\n\n💥 Test suite failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
