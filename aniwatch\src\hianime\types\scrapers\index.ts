import type { ScrapedHomePage } from "./homePage.js";
import type { ScrapedGenreAnime } from "./animeGenre.js";
import type { ScrapedAnimeAZList } from "./animeAZList.js";
import type { ScrapedAnimeQtipInfo } from "./animeQtip.js";
import type { ScrapedAnimeEpisodes } from "./animeEpisodes.js";
import type { ScrapedAnimeCategory } from "./animeCategory.js";
import type { ScrapedProducerAnime } from "./animeProducer.js";
import type { ScrapedEpisodeServers } from "./episodeServers.js";
import type { ScrapedAnimeAboutInfo } from "./animeAboutInfo.js";
import type { ScrapedAnimeSearchResult } from "./animeSearch.js";
import type { ScrapedAnimeEpisodesSources } from "./animeEpisodeSrcs.js";
import type { ScrapedAnimeSearchSuggestion } from "./animeSearchSuggestion.js";
import type {
    ScrapedEstimatedSchedule,
    ScrapedNextEpisodeSchedule,
} from "./estimatedSchedule.js";

export type {
    ScrapedHomePage,
    ScrapedGenreAnime,
    ScrapedAnimeAZList,
    ScrapedAnimeQtipInfo,
    ScrapedAnimeEpisodes,
    ScrapedProducerAnime,
    ScrapedAnimeCategory,
    ScrapedEpisodeServers,
    ScrapedAnimeAboutInfo,
    ScrapedAnimeSearchResult,
    ScrapedEstimatedSchedule,
    ScrapedNextEpisodeSchedule,
    ScrapedAnimeEpisodesSources,
    ScrapedAnimeSearchSuggestion,
};
