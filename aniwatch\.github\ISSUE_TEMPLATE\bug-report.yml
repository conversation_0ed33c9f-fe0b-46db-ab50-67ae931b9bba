name: Bug report 🐛
description: Create a report to help this package improve
labels: [bug]
assignees: [ghoshRitesh12]
body:
  - type: input
    id: describe-the-bug
    attributes:
      label: Describe the bug
      description: |
        A clear and concise description of what the bug is.
      placeholder: |
        Example: "This scraper is not working..."
    validations:
      required: true

  - type: textarea
    id: expected-behavior
    attributes:
      label: Expected behavior
      placeholder: |
        Example: 
          "This should happen..."
    validations:
      required: true

  - type: textarea
    id: actual-behavior
    attributes:
      label: Actual behavior
      placeholder: |
        Example:
          "This happened instead..."
    validations:
      required: true

  - type: textarea
    id: additional-context
    attributes:
      label: Additional context
      description: |
        Add any other context about the problem here.
      placeholder: |
        Example:
          "Also ..."
