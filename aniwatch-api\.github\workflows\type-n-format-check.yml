name: "Type and format check"

on:
  push:
    branches: ["main"]
  pull_request:
    branches: ["main"]

jobs:
  lint_check:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repo
        uses: actions/checkout@v4

      - name: Setup NodeJS
        uses: actions/setup-node@v4
        with:
          node-version: "22.x"

      - name: Install deps
        run: npm i

      - name: Check types
        # This step checks for TypeScript type errors
        run: npm run typecheck

      - name: Check prettier
        run: npm run format:check
