#!/usr/bin/env python3
"""
Simple test for anime episode sources
"""

import logging
logging.basicConfig(level=logging.INFO)

try:
    from scrapers.animeEpisodeSources import get_anime_episode_sources, AnimeEpisodeSourcesError
    print("✅ Import successful")
    
    # Test with a simple episode ID
    episode_id = "attack-on-titan-112?ep=3304"
    print(f"🧪 Testing with episode ID: {episode_id}")
    
    try:
        result = get_anime_episode_sources(episode_id, "hd-1", "sub")
        print(f"📊 Result type: {type(result)}")
        print(f"📊 Success: {result.get('success', False)}")
        
        if result.get("success"):
            data = result.get("data", {})
            sources = data.get("sources", [])
            print(f"📊 Found {len(sources)} sources")
            
            if sources:
                first_source = sources[0]
                print(f"📊 First source URL: {first_source.get('url', 'N/A')[:100]}...")
                print(f"📊 First source quality: {first_source.get('quality', 'N/A')}")
        else:
            print(f"❌ Error: {result.get('error', 'Unknown error')}")
            
    except AnimeEpisodeSourcesError as e:
        print(f"❌ AnimeEpisodeSourcesError: {e.message}")
    except Exception as e:
        print(f"❌ Exception: {str(e)}")
        import traceback
        traceback.print_exc()
        
except ImportError as e:
    print(f"❌ Import error: {str(e)}")
except Exception as e:
    print(f"❌ Unexpected error: {str(e)}")
    import traceback
    traceback.print_exc()

print("🏁 Test completed")
