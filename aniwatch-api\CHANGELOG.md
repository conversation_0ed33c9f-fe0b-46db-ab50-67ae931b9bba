# [2.15.0](https://github.com/ghoshRitesh12/aniwatch-api/compare/v2.14.5...v2.15.0) (2025-05-25)


### Features

* add `execGracefulShutdown` method & `Prettify<T>` type ([a3cd5a3](https://github.com/ghoshRitesh12/aniwatch-api/commit/a3cd5a34aada2331e3f9bd56798f1ddb979dbb01))
* add cache response header if caching is enabled ([013196b](https://github.com/ghoshRitesh12/aniwatch-api/commit/013196bd3d5e44aa76b1825f24f9f5186fe7e7c1))
* add deployment env based rate limiters ([f5d7462](https://github.com/ghoshRitesh12/aniwatch-api/commit/f5d74627e8b7f844f58a9dc82e3a79ecd911cc8f))
* add flexible `ANIWATCH_API_DEPLOYMENT_ENV` env and deployment envs ([bd597a6](https://github.com/ghoshRitesh12/aniwatch-api/commit/bd597a6fd71347c9cef687a891a408f40e6cbd93))
* add graceful shutdown and better deployment environment logic ([536b97e](https://github.com/ghoshRitesh12/aniwatch-api/commit/536b97e549675e1f0b035a5139320d6d60780608))
* add graceful shutdown and better deployment environment logic ([b19078e](https://github.com/ghoshRitesh12/aniwatch-api/commit/b19078e29cbd86d46d4ef1c5e5c6e914d1c8cc2f))
* update imp scripts, description & packages ([cdd05c7](https://github.com/ghoshRitesh12/aniwatch-api/commit/cdd05c719ba361cc28acf6c4a7d535c64a70e604))
* update response to include status instead of success for consistency ([74c9ffc](https://github.com/ghoshRitesh12/aniwatch-api/commit/74c9ffcb8f62dcdae6983da490436c39b6b8e558))



## [2.14.5](https://github.com/ghoshRitesh12/aniwatch-api/compare/v2.14.4...v2.14.5) (2025-05-11)


### Bug Fixes

* update `ANIWATCH_API_HOSTNAME`§ env example ([d2812d1](https://github.com/ghoshRitesh12/aniwatch-api/commit/d2812d13bf5d285ce294d39a48d01020437f30ab))



## [2.14.4](https://github.com/ghoshRitesh12/aniwatch-api/compare/v2.14.3...v2.14.4) (2025-05-11)


### Bug Fixes

* update `ANIWATCH_API_HOSTNAME` env ([1081c7b](https://github.com/ghoshRitesh12/aniwatch-api/commit/1081c7b7bb7305ab5440301ecba8728fe88af90b))



## [2.14.3](https://github.com/ghoshRitesh12/aniwatch-api/compare/v2.14.2...v2.14.3) (2025-05-11)



## [2.14.2](https://github.com/ghoshRitesh12/aniwatch-api/compare/v2.14.1...v2.14.2) (2025-05-11)



