#!/usr/bin/env python3
"""
Test the enhanced extractors
"""

import logging
from scrapers.extractors import MegaCloudExtractor, RapidCloudExtractor, ExtractorFactory

logging.basicConfig(level=logging.INFO)

def test_megacloud_extractor():
    """Test MegaCloud extractor with a sample URL"""
    print("🧪 Testing MegaCloud Extractor...")
    
    # Sample MegaCloud URL (similar to what you encountered)
    test_url = "https://megacloud.blog/embed-2/v2/e-1/NxSK1hC8AaR5?k=1"
    
    try:
        extractor = MegaCloudExtractor()
        result = extractor.extract(test_url)
        
        print(f"✅ Extraction completed")
        print(f"📊 Found {len(result.get('sources', []))} source(s)")
        
        for i, source in enumerate(result.get('sources', []), 1):
            print(f"   Source {i}:")
            print(f"     URL: {source.get('url', 'N/A')[:100]}...")
            print(f"     Quality: {source.get('quality', 'N/A')}")
            print(f"     Type: {source.get('type', 'N/A')}")
            if 'note' in source:
                print(f"     Note: {source['note']}")
            if 'error' in source:
                print(f"     Error: {source['error']}")
        
        print(f"📊 Found {len(result.get('subtitles', []))} subtitle(s)")
        print(f"📊 Embed URL: {result.get('embedURL', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def test_rapidcloud_extractor():
    """Test RapidCloud extractor"""
    print("🧪 Testing RapidCloud Extractor...")
    
    # Sample RapidCloud URL
    test_url = "https://rapid-cloud.co/embed-6/ajax/embed-6/getSources?id=test123"
    
    try:
        extractor = RapidCloudExtractor()
        result = extractor.extract(test_url)
        
        print(f"✅ Extraction completed")
        print(f"📊 Found {len(result.get('sources', []))} source(s)")
        
        for i, source in enumerate(result.get('sources', []), 1):
            print(f"   Source {i}:")
            print(f"     URL: {source.get('url', 'N/A')[:100]}...")
            print(f"     Quality: {source.get('quality', 'N/A')}")
            print(f"     Type: {source.get('type', 'N/A')}")
            if 'note' in source:
                print(f"     Note: {source['note']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def test_extractor_factory():
    """Test the ExtractorFactory"""
    print("🧪 Testing ExtractorFactory...")
    
    test_urls = [
        "https://megacloud.blog/embed-2/v2/e-1/test",
        "https://rapid-cloud.co/embed-6/test", 
        "https://streamsb.net/embed/test",
        "https://streamtape.com/embed/test",
        "https://unknown-domain.com/test"
    ]
    
    try:
        for url in test_urls:
            extractor = ExtractorFactory.get_extractor(url)
            print(f"✅ {url} -> {type(extractor).__name__}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def test_extract_sources():
    """Test the extract_sources method"""
    print("🧪 Testing ExtractorFactory.extract_sources...")
    
    test_url = "https://megacloud.blog/embed-2/v2/e-1/NxSK1hC8AaR5?k=1"
    
    try:
        result = ExtractorFactory.extract_sources(test_url)
        
        print(f"✅ Extraction completed")
        print(f"📊 Result type: {type(result)}")
        print(f"📊 Has sources: {'sources' in result}")
        print(f"📊 Has subtitles: {'subtitles' in result}")
        print(f"📊 Has embedURL: {'embedURL' in result}")
        
        if 'sources' in result:
            print(f"📊 Found {len(result['sources'])} source(s)")
            for i, source in enumerate(result['sources'][:2], 1):  # Show first 2
                print(f"   Source {i}: {source.get('type', 'unknown')} - {source.get('quality', 'auto')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

if __name__ == "__main__":
    print("🎬 Enhanced Extractors Test Suite")
    print("=" * 60)
    
    tests = [
        test_megacloud_extractor,
        test_rapidcloud_extractor,
        test_extractor_factory,
        test_extract_sources
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        print()
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test failed with exception: {str(e)}")
        print("-" * 40)
    
    print(f"\n🏁 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All extractor tests passed!")
    else:
        print("⚠️  Some tests failed. Check the output above.")
    
    print("\n📝 Note: The extractors now provide more detailed information")
    print("   about embed URLs and will attempt to extract actual video sources")
    print("   when possible. Embed URLs are returned as fallbacks when direct")
    print("   extraction is not possible.")
