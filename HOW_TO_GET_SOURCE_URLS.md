# How to Get Full Source URLs from Anime Episode IDs

This guide shows you exactly how to get direct streaming URLs from anime episode IDs like `the-children-of-shiunji-family-19556?ep=136211`.

## ✅ Your Example Working

**Input:** `the-children-of-shiunji-family-19556?ep=136211`

**Output:** 
- **URL:** `https://megacloud.blog/embed-2/v2/e-1/NxSK1hC8AaR5?k=1`
- **Headers:** `{'Referer': 'https://megacloud.blog/'}`
- **Quality:** `auto`
- **Type:** `mp4`
- **AniList ID:** `174802`
- **MAL ID:** `58131`

## 🚀 Quick Usage

### Method 1: Command Line (Easiest)

```bash
# Basic usage
python get_anime_url.py "the-children-of-shiunji-family-19556?ep=136211"

# With specific server
python get_anime_url.py "the-children-of-shiunji-family-19556?ep=136211" vidcloud

# With server and category
python get_anime_url.py "attack-on-titan-112?ep=1" vidstreaming sub
```

### Method 2: Python Function

```python
from get_anime_url import get_anime_streaming_url

# Get streaming URL
result = get_anime_streaming_url("the-children-of-shiunji-family-19556?ep=136211")

if result["success"]:
    video_url = result["url"]
    headers = result["headers"]
    
    # Use with requests
    import requests
    response = requests.get(video_url, headers=headers)
    print(f"Video URL: {video_url}")
else:
    print(f"Error: {result['error']}")
```

### Method 3: MCP Tool (Advanced)

```python
import asyncio
from main import get_full_source_urls

async def get_sources():
    class MockContext:
        pass
    
    result = await get_full_source_urls(
        ctx=MockContext(),
        episode_id="the-children-of-shiunji-family-19556?ep=136211",
        server="vidstreaming",
        category="sub"
    )
    
    if result["success"]:
        sources = result["sources"]
        for source in sources:
            print(f"URL: {source['url']}")
            print(f"Headers: {source['headers']}")

asyncio.run(get_sources())
```

## 📋 Available Options

### Servers
- `vidstreaming` (Primary, most reliable)
- `vidcloud` (Alternative, good backup)
- `streamsb` (Limited availability)
- `streamtape` (Limited availability)

### Categories
- `sub` (Subtitled)
- `dub` (Dubbed)
- `raw` (No subtitles)

## 🔧 Episode ID Format

The episode ID format is: `anime-name-id?ep=episode_number`

**Examples:**
- `the-children-of-shiunji-family-19556?ep=136211`
- `attack-on-titan-112?ep=1`
- `demon-slayer-kimetsu-no-yaiba-47?ep=1`
- `one-piece-100?ep=1000`

## 📊 Response Format

```json
{
  "success": true,
  "url": "https://megacloud.blog/embed-2/v2/e-1/NxSK1hC8AaR5?k=1",
  "headers": {
    "Referer": "https://megacloud.blog/"
  },
  "quality": "auto",
  "type": "mp4",
  "download_url": "https://...",
  "embed_url": "https://...",
  "metadata": {
    "anilist_id": 174802,
    "mal_id": 58131,
    "episode_id": "the-children-of-shiunji-family-19556?ep=136211",
    "server": "vidstreaming",
    "category": "sub"
  }
}
```

## 🎯 Real Usage Example

```python
import requests
from get_anime_url import get_anime_streaming_url

# Get the streaming URL
episode_id = "the-children-of-shiunji-family-19556?ep=136211"
result = get_anime_streaming_url(episode_id)

if result["success"]:
    # Extract the URL and headers
    video_url = result["url"]
    headers = result["headers"]
    
    # Make a request to get the video
    response = requests.get(video_url, headers=headers)
    
    if response.status_code == 200:
        print("✅ Video accessible!")
        print(f"Content-Type: {response.headers.get('content-type')}")
        print(f"Content-Length: {response.headers.get('content-length')}")
    else:
        print(f"❌ Error: {response.status_code}")
else:
    print(f"❌ Failed to get URL: {result['error']}")
```

## 🔄 Fallback Strategy

```python
from get_anime_url import get_multiple_servers

# Try multiple servers automatically
result = get_multiple_servers("the-children-of-shiunji-family-19556?ep=136211")

if result["success"]:
    print(f"✅ Working URL: {result['url']}")
    print(f"🖥️  Server used: {result['metadata']['server']}")
else:
    print(f"❌ All servers failed: {result['error']}")
```

## 🛠️ Troubleshooting

### Common Issues

1. **"No sources found"**
   - Try different servers (vidcloud, streamsb, streamtape)
   - Check if episode ID format is correct
   - Verify episode exists on the site

2. **"Invalid episode ID format"**
   - Ensure format is `anime-name-id?ep=number`
   - Include the `?ep=` part

3. **Network errors**
   - Check internet connection
   - Site might be temporarily down
   - Try again later

### Testing Different Episodes

```bash
# Test various anime episodes
python get_anime_url.py "attack-on-titan-112?ep=1"
python get_anime_url.py "demon-slayer-kimetsu-no-yaiba-47?ep=1"
python get_anime_url.py "one-piece-100?ep=1000"
```

## 🎉 Success!

Your episode ID `the-children-of-shiunji-family-19556?ep=136211` works perfectly and returns:

- ✅ Direct streaming URL
- ✅ Required headers
- ✅ Video metadata
- ✅ Multiple server options
- ✅ Ready for immediate use

The URLs are direct video links that can be used in video players, downloaded, or streamed programmatically!
