#!/usr/bin/env python3
"""
Test script for anime episode sources functionality
"""

import sys
import logging
from scrapers.animeEpisodeSources import get_anime_episode_sources, AnimeEpisodeSourcesError

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_episode_sources():
    """Test the episode sources functionality"""
    
    # Test cases
    test_cases = [
        {
            "name": "Attack on Titan Episode",
            "episode_id": "attack-on-titan-112?ep=3303",
            "server": "hd-1",
            "category": "sub"
        },
        {
            "name": "The Children of Shiunji Family Episode",
            "episode_id": "the-children-of-shiunji-family-19556?ep=136211",
            "server": "hd-1", 
            "category": "sub"
        },
        {
            "name": "Invalid Episode ID",
            "episode_id": "invalid-episode-id",
            "server": "hd-1",
            "category": "sub"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{'='*60}")
        print(f"Test {i}: {test_case['name']}")
        print(f"{'='*60}")
        print(f"Episode ID: {test_case['episode_id']}")
        print(f"Server: {test_case['server']}")
        print(f"Category: {test_case['category']}")
        print("-" * 60)
        
        try:
            result = get_anime_episode_sources(
                episode_id=test_case['episode_id'],
                server=test_case['server'],
                category=test_case['category']
            )
            
            if result.get("success"):
                data = result.get("data", {})
                sources = data.get("sources", [])
                headers = data.get("headers", {})
                
                print(f"✅ SUCCESS")
                print(f"📊 Found {len(sources)} source(s)")
                
                for j, source in enumerate(sources[:3], 1):  # Show first 3 sources
                    print(f"   Source {j}:")
                    print(f"     URL: {source.get('url', 'N/A')[:100]}...")
                    print(f"     Quality: {source.get('quality', 'N/A')}")
                    print(f"     Type: {'M3U8' if source.get('isM3U8') else 'MP4'}")
                
                if len(sources) > 3:
                    print(f"   ... and {len(sources) - 3} more source(s)")
                
                print(f"🔗 Headers: {len(headers)} header(s)")
                for key, value in list(headers.items())[:2]:  # Show first 2 headers
                    print(f"   {key}: {value}")
                
                # Show metadata if available
                if data.get("anilistID"):
                    print(f"📺 AniList ID: {data['anilistID']}")
                if data.get("malID"):
                    print(f"📺 MAL ID: {data['malID']}")
                    
            else:
                print(f"❌ FAILED")
                print(f"Error: {result.get('error', 'Unknown error')}")
                print(f"Error Type: {result.get('error_type', 'Unknown')}")
                
        except AnimeEpisodeSourcesError as e:
            print(f"❌ ANIME EPISODE SOURCES ERROR")
            print(f"Message: {e.message}")
            print(f"Function: {e.function_name}")
            print(f"Status Code: {e.status_code}")
            
        except Exception as e:
            print(f"❌ UNEXPECTED ERROR")
            print(f"Error: {str(e)}")
            print(f"Type: {type(e).__name__}")

def test_server_validation():
    """Test server validation"""
    print(f"\n{'='*60}")
    print("Server Validation Tests")
    print(f"{'='*60}")
    
    valid_servers = ["hd-1", "megacloud", "streamsb", "streamtape", "hd-2"]
    invalid_servers = ["invalid-server", "youtube", ""]
    
    episode_id = "attack-on-titan-112?ep=3304"
    
    print("Testing valid servers:")
    for server in valid_servers:
        try:
            result = get_anime_episode_sources(episode_id, server, "sub")
            status = "✅ ACCEPTED" if result.get("success") else f"❌ FAILED: {result.get('error', 'Unknown')}"
            print(f"  {server}: {status}")
        except Exception as e:
            print(f"  {server}: ❌ ERROR: {str(e)}")
    
    print("\nTesting invalid servers:")
    for server in invalid_servers:
        try:
            result = get_anime_episode_sources(episode_id, server, "sub")
            status = "❌ SHOULD HAVE FAILED" if result.get("success") else f"✅ REJECTED: {result.get('error', 'Unknown')}"
            print(f"  '{server}': {status}")
        except Exception as e:
            print(f"  '{server}': ✅ REJECTED: {str(e)}")

if __name__ == "__main__":
    print("🎬 Anime Episode Sources Test Suite")
    print("=" * 60)
    
    try:
        test_episode_sources()
        test_server_validation()
        
        print(f"\n{'='*60}")
        print("🎉 Test suite completed!")
        print("Check the results above for any issues.")
        print(f"{'='*60}")
        
    except KeyboardInterrupt:
        print("\n\n⚠️  Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n💥 Test suite failed with error: {str(e)}")
        sys.exit(1)
