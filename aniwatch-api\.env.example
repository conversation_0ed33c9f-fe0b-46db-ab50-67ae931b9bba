# port number of the aniwatch api
ANIWATCH_API_PORT=4000

# env to control allowed origins
ANIWATCH_API_CORS_ALLOWED_ORIGINS=<https://your-production-domain.com,https://another-trusted-domain.com>

# RATE LIMIT
# duration to track requests (in milliseconds) for rate limiting. here, 30*60*1000 = 1800000 = 30 minutes
ANIWATCH_API_WINDOW_MS=1800000 
# maximum number of requests in this timeperiod
ANIWATCH_API_MAX_REQS=70 

# CAUTION: 
#   For personal deployments, if you wanna have rate limitting
#   in your application, then set the env below to your deployed 
#   instance's hostname, otherwise don't set or have it at all.
#   If you set the env below to an incorrect value, you may face other issues.
# ANIWATCH_API_HOSTNAME=<https://your-production-domain.com>


# NOTE:
#   The deployment environment of the Aniwatch API.
#   Many configurations depend on this env, rate limiting being one of them. 
#   It must be set incase of serverless deployments; otherwise, you may run into weird issues.
#   Possible values: 'nodejs' | 'docker' | 'vercel' | 'render' | 'cloudflare-workers'
ANIWATCH_API_DEPLOYMENT_ENV="nodejs"

# env to use optional redis caching functionality
ANIWATCH_API_REDIS_CONN_URL=<rediss://default:<EMAIL>:6379>

# Cache-Control settings for Vercel Edge Caching
# Specifies the maximum amount of time (in seconds) a resource is considered fresh when served by a CDN cache.
ANIWATCH_API_S_MAXAGE=60
# Specifies the amount of time (in seconds) a resource is served stale while a new one is fetched.
ANIWATCH_API_STALE_WHILE_REVALIDATE=30
