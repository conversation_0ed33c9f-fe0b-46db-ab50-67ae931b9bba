#!/usr/bin/env python3
"""
Test the structure and imports without making network requests
"""

import logging
logging.basicConfig(level=logging.INFO)

def test_imports():
    """Test that all imports work correctly"""
    print("🧪 Testing imports...")
    
    try:
        from scrapers.animeEpisodeSources import (
            get_anime_episode_sources, 
            AnimeEpisodeSourcesError, 
            Servers,
            ScrapedAnimeEpisodesSources,
            Video,
            Subtitle,
            Intro
        )
        print("✅ All imports successful")
        return True
    except ImportError as e:
        print(f"❌ Import error: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error during import: {str(e)}")
        return False

def test_data_structures():
    """Test that data structures can be created"""
    print("🧪 Testing data structures...")
    
    try:
        from scrapers.animeEpisodeSources import Video, Subtitle, Intro, ScrapedAnimeEpisodesSources
        
        # Test Video
        video = Video(url="https://example.com/video.mp4", quality="720p")
        print(f"✅ Video: {video.url}, {video.quality}")
        
        # Test Subtitle
        subtitle = Subtitle(url="https://example.com/sub.vtt", lang="en")
        print(f"✅ Subtitle: {subtitle.url}, {subtitle.lang}")
        
        # Test Intro
        intro = Intro(start=10, end=90)
        print(f"✅ Intro: {intro.start}-{intro.end}")
        
        # Test main structure
        sources = ScrapedAnimeEpisodesSources()
        sources.sources = [video]
        sources.subtitles = [subtitle]
        sources.intro = intro
        print(f"✅ ScrapedAnimeEpisodesSources with {len(sources.sources)} sources")
        
        return True
    except Exception as e:
        print(f"❌ Error testing data structures: {str(e)}")
        return False

def test_servers_enum():
    """Test the Servers enum"""
    print("🧪 Testing Servers enum...")
    
    try:
        from scrapers.animeEpisodeSources import Servers
        
        print(f"✅ VID_STREAMING: {Servers.VID_STREAMING.value}")
        print(f"✅ MEGA_CLOUD: {Servers.MEGA_CLOUD.value}")
        print(f"✅ STREAM_SB: {Servers.STREAM_SB.value}")
        print(f"✅ STREAM_TAPE: {Servers.STREAM_TAPE.value}")
        print(f"✅ VID_CLOUD: {Servers.VID_CLOUD.value}")
        
        # Test enum conversion
        server = Servers("hd-1")
        print(f"✅ Enum conversion: 'hd-1' -> {server}")
        
        return True
    except Exception as e:
        print(f"❌ Error testing Servers enum: {str(e)}")
        return False

def test_validation():
    """Test input validation without network requests"""
    print("🧪 Testing input validation...")
    
    try:
        from scrapers.animeEpisodeSources import get_anime_episode_sources, AnimeEpisodeSourcesError
        
        # Test invalid episode ID
        try:
            result = get_anime_episode_sources("invalid-id", "hd-1", "sub")
            if not result.get("success"):
                print(f"✅ Invalid episode ID rejected: {result.get('error', 'Unknown error')}")
            else:
                print("❌ Invalid episode ID should have been rejected")
        except AnimeEpisodeSourcesError as e:
            print(f"✅ Invalid episode ID rejected with AnimeEpisodeSourcesError: {e.message}")
        except Exception as e:
            print(f"✅ Invalid episode ID rejected with exception: {str(e)}")
        
        # Test invalid server
        try:
            result = get_anime_episode_sources("test?ep=1", "invalid-server", "sub")
            if not result.get("success"):
                print(f"✅ Invalid server rejected: {result.get('error', 'Unknown error')}")
            else:
                print("❌ Invalid server should have been rejected")
        except Exception as e:
            print(f"✅ Invalid server rejected: {str(e)}")
        
        # Test invalid category
        try:
            result = get_anime_episode_sources("test?ep=1", "hd-1", "")
            if not result.get("success"):
                print(f"✅ Invalid category rejected: {result.get('error', 'Unknown error')}")
            else:
                print("❌ Invalid category should have been rejected")
        except Exception as e:
            print(f"✅ Invalid category rejected: {str(e)}")
        
        return True
    except Exception as e:
        print(f"❌ Error testing validation: {str(e)}")
        return False

def test_mcp_integration():
    """Test MCP integration"""
    print("🧪 Testing MCP integration...")
    
    try:
        import main
        print("✅ Main module imported successfully")
        
        # Check if the functions are defined
        if hasattr(main, 'get_anime_episode_sources'):
            print("✅ get_anime_episode_sources function found in main")
        else:
            print("❌ get_anime_episode_sources function not found in main")
            
        if hasattr(main, 'get_available_servers'):
            print("✅ get_available_servers function found in main")
        else:
            print("❌ get_available_servers function not found in main")
        
        return True
    except Exception as e:
        print(f"❌ Error testing MCP integration: {str(e)}")
        return False

if __name__ == "__main__":
    print("🎬 Anime Episode Sources Structure Test")
    print("=" * 60)
    
    tests = [
        test_imports,
        test_data_structures,
        test_servers_enum,
        test_validation,
        test_mcp_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        print()
        if test():
            passed += 1
        print("-" * 40)
    
    print(f"\n🏁 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All structure tests passed!")
    else:
        print("⚠️  Some tests failed. Check the output above.")
