#!/usr/bin/env python3
"""
Demo: Get Full Source URL from Anime Episode ID
Example: the-children-of-shiunji-family-19556?ep=136211
"""

import asyncio
import sys
import os

# Add the current directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def get_source_url_demo():
    """Demonstrate getting full source URL from episode ID"""
    
    print("🎬 Anime Source URL Extractor")
    print("=" * 50)
    
    # Your example episode ID
    episode_id = "the-children-of-shiunji-family-19556?ep=136211"
    
    print(f"📺 Input: {episode_id}")
    print(f"🎯 Goal: Get direct streaming URL")
    print("-" * 50)
    
    try:
        # Method 1: Using MCP Tool (Recommended)
        print("\n🔧 Method 1: Using MCP Tool")
        print("-" * 30)
        
        from main import get_full_source_urls
        
        class MockContext:
            pass
        
        result = await get_full_source_urls(
            ctx=MockContext(),
            episode_id=episode_id,
            server="vidstreaming",
            category="sub"
        )
        
        if result.get('success'):
            sources = result.get('sources', [])
            if sources:
                source = sources[0]  # Get first source
                
                print(f"✅ SUCCESS!")
                print(f"📊 Full URL: {source['url']}")
                print(f"🎬 Quality: {source['quality']}")
                print(f"📝 Type: {source['type']}")
                print(f"🔗 Required Headers: {source['headers']}")
                print(f"✨ Ready to Use: {source['ready_to_use']}")
                
                # Show metadata
                metadata = result.get('metadata', {})
                print(f"\n📋 Metadata:")
                print(f"   AniList ID: {metadata.get('anilist_id')}")
                print(f"   MAL ID: {metadata.get('mal_id')}")
                
                # Show how to use it
                print(f"\n💡 Usage Example:")
                print(f"   import requests")
                print(f"   headers = {source['headers']}")
                print(f"   response = requests.get('{source['url']}', headers=headers)")
                
        else:
            print(f"❌ Error: {result.get('error')}")
        
        # Method 2: Direct Scraper
        print(f"\n🔧 Method 2: Direct Scraper")
        print("-" * 30)
        
        from scrapers.animeEpisodeSources import get_anime_episode_sources
        
        direct_result = get_anime_episode_sources(episode_id, "vidstreaming", "sub")
        
        if direct_result:
            sources = direct_result.get("sources", [])
            if sources:
                source = sources[0]
                print(f"✅ SUCCESS!")
                print(f"📊 Direct URL: {source['url']}")
                print(f"🔗 Headers: {direct_result.get('headers', {})}")
        
        # Test with different servers
        print(f"\n🔧 Testing Different Servers")
        print("-" * 30)
        
        servers = ["vidstreaming", "vidcloud"]
        
        for server in servers:
            print(f"\n🖥️  Testing {server} server:")
            
            server_result = await get_full_source_urls(
                ctx=MockContext(),
                episode_id=episode_id,
                server=server,
                category="sub"
            )
            
            if server_result.get('success'):
                server_sources = server_result.get('sources', [])
                if server_sources:
                    server_source = server_sources[0]
                    print(f"   ✅ URL: {server_source['url'][:60]}...")
                    print(f"   🎬 Quality: {server_source['quality']}")
                else:
                    print(f"   ❌ No sources found")
            else:
                print(f"   ❌ Error: {server_result.get('error')}")
        
        print(f"\n" + "=" * 50)
        print(f"🎉 Demo Complete!")
        print(f"💡 Your episode ID works perfectly!")
        print(f"🔗 You now have direct streaming URLs!")
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()

def simple_sync_example():
    """Simple synchronous example"""
    print(f"\n🔧 Simple Sync Example")
    print("-" * 30)
    
    try:
        from scrapers.animeEpisodeSources import get_anime_episode_sources
        
        # Your episode ID
        episode_id = "the-children-of-shiunji-family-19556?ep=136211"
        
        # Get sources
        result = get_anime_episode_sources(episode_id, "vidstreaming", "sub")
        
        # Extract the URL
        if result and result.get("sources"):
            video_url = result["sources"][0]["url"]
            headers = result.get("headers", {})
            
            print(f"✅ Video URL: {video_url}")
            print(f"🔗 Headers: {headers}")
            print(f"🎬 Ready to stream!")
            
            return video_url, headers
        else:
            print(f"❌ No sources found")
            return None, None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return None, None

if __name__ == "__main__":
    # Run async demo
    asyncio.run(get_source_url_demo())
    
    # Run simple sync example
    simple_sync_example()
