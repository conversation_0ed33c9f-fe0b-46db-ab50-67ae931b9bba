name: "Docker Continuous Deployment"

on:
  release:
    types: [published]

jobs:
  build_docker_image:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GHCR_DOCKER_PWD }}

      - name: Build and push docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          push: true
          tags: |
            ghcr.io/ghoshritesh12/aniwatch:${{ github.event.release.tag_name }}
            ghcr.io/ghoshritesh12/aniwatch:latest

  trigger_deploy:
    needs: build_docker_image
    runs-on: ubuntu-latest

    steps:
      - name: Trigger render deploy
        run: |
          curl --request POST \
            --url https://api.render.com/v1/services/${{ secrets.RENDER_API_SERVICE_ID }}/deploys \
            --header 'accept: application/json' \
            --header 'authorization: Bearer ${{ secrets.RENDER_API_KEY }}' \
            --header 'content-type: application/json' \
            --data '{ "clearCache": "clear" }'
