"""
Video extractors for different streaming servers
Based on the TypeScript extractors from the aniwatch implementation
"""

import re
import json
import base64
import requests
from urllib.parse import urlparse, parse_qs, urljoin
from typing import Dict, List, Optional, Any, Union
from .constants import USER_AGENT_HEADER

class ExtractorError(Exception):
    """Base exception for extractor errors"""
    pass

class BaseExtractor:
    """Base class for video extractors"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            "User-Agent": USER_AGENT_HEADER
        })
    
    def extract(self, url: str) -> Dict[str, Any]:
        """Extract video sources from URL"""
        raise NotImplementedError("Subclasses must implement extract method")

class MegaCloudExtractor(BaseExtractor):
    """
    Extractor for MegaCloud/VidStreaming servers
    Enhanced version that attempts to extract actual video sources
    """

    def __init__(self):
        super().__init__()
        self.base_url = "https://megacloud.tv"

    def extract(self, url: str) -> Dict[str, Any]:
        """Extract sources from MegaCloud URL"""
        try:
            parsed_url = urlparse(url)

            # Try to get the embed page content
            response = self.session.get(url, headers={
                "Referer": "https://hianime.sx/",
                "User-Agent": USER_AGENT_HEADER,
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"
            }, timeout=10)
            response.raise_for_status()

            content = response.text
            sources = []
            subtitles = []

            # Look for JSON data containing sources
            # MegaCloud typically embeds source data in JavaScript
            json_patterns = [
                r'sources:\s*(\[.*?\])',
                r'"sources":\s*(\[.*?\])',
                r'file:\s*["\']([^"\']+)["\']',
                r'src:\s*["\']([^"\']+)["\']'
            ]

            for pattern in json_patterns:
                matches = re.findall(pattern, content, re.DOTALL)
                for match in matches:
                    try:
                        if match.startswith('['):
                            # Try to parse as JSON array
                            source_data = json.loads(match)
                            if isinstance(source_data, list):
                                for item in source_data:
                                    if isinstance(item, dict) and 'file' in item:
                                        sources.append({
                                            "url": item['file'],
                                            "quality": item.get('label', 'auto'),
                                            "type": "hls" if item['file'].endswith('.m3u8') else "mp4"
                                        })
                        else:
                            # Single URL
                            if match.startswith('http'):
                                sources.append({
                                    "url": match,
                                    "quality": "auto",
                                    "type": "hls" if match.endswith('.m3u8') else "mp4"
                                })
                    except (json.JSONDecodeError, KeyError):
                        continue

            # Look for subtitle tracks
            subtitle_patterns = [
                r'tracks:\s*(\[.*?\])',
                r'"tracks":\s*(\[.*?\])'
            ]

            for pattern in subtitle_patterns:
                matches = re.findall(pattern, content, re.DOTALL)
                for match in matches:
                    try:
                        track_data = json.loads(match)
                        if isinstance(track_data, list):
                            for track in track_data:
                                if isinstance(track, dict) and track.get('kind') == 'captions':
                                    subtitles.append({
                                        "url": track.get('file', ''),
                                        "lang": track.get('label', 'Unknown'),
                                        "id": track.get('label', '').lower()
                                    })
                    except (json.JSONDecodeError, KeyError):
                        continue

            # If no sources found through parsing, try to extract m3u8 URLs directly
            if not sources:
                m3u8_pattern = r'(https?://[^\s"\'<>]+\.m3u8[^\s"\'<>]*)'
                m3u8_matches = re.findall(m3u8_pattern, content)
                for m3u8_url in m3u8_matches:
                    sources.append({
                        "url": m3u8_url,
                        "quality": "auto",
                        "type": "hls"
                    })

            # If still no sources, return the embed URL as a fallback
            if not sources:
                sources.append({
                    "url": url,
                    "quality": "auto",
                    "type": "embed",
                    "note": "Embed URL - may require additional processing"
                })

            return {
                "sources": sources,
                "subtitles": subtitles,
                "intro": {
                    "start": 0,
                    "end": 0
                },
                "embedURL": url
            }

        except Exception as e:
            # Fallback: return the original URL
            return {
                "sources": [
                    {
                        "url": url,
                        "quality": "auto",
                        "type": "embed",
                        "error": str(e),
                        "note": "Extraction failed - returning embed URL"
                    }
                ],
                "subtitles": [],
                "embedURL": url
            }

class RapidCloudExtractor(BaseExtractor):
    """
    Extractor for RapidCloud/VidCloud servers
    Enhanced version that attempts to extract actual video sources
    """

    def extract(self, url: str) -> Dict[str, Any]:
        """Extract sources from RapidCloud URL"""
        try:
            # Try to get the embed page content
            response = self.session.get(url, headers={
                "Referer": "https://hianime.sx/",
                "User-Agent": USER_AGENT_HEADER,
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"
            }, timeout=10)
            response.raise_for_status()

            content = response.text
            sources = []
            subtitles = []

            # Look for video sources in the page
            # RapidCloud often has sources in JavaScript variables
            source_patterns = [
                r'file:\s*["\']([^"\']+\.m3u8[^"\']*)["\']',
                r'src:\s*["\']([^"\']+\.m3u8[^"\']*)["\']',
                r'source:\s*["\']([^"\']+\.m3u8[^"\']*)["\']',
                r'(https?://[^\s"\'<>]+\.m3u8[^\s"\'<>]*)'
            ]

            for pattern in source_patterns:
                matches = re.findall(pattern, content)
                for match in matches:
                    if match.startswith('http') and '.m3u8' in match:
                        sources.append({
                            "url": match,
                            "quality": "auto",
                            "type": "hls"
                        })

            # Remove duplicates
            seen_urls = set()
            unique_sources = []
            for source in sources:
                if source["url"] not in seen_urls:
                    seen_urls.add(source["url"])
                    unique_sources.append(source)

            # If no sources found, return the embed URL
            if not unique_sources:
                unique_sources.append({
                    "url": url,
                    "quality": "auto",
                    "type": "embed",
                    "note": "Embed URL - may require additional processing"
                })

            return {
                "sources": unique_sources,
                "subtitles": subtitles,
                "embedURL": url
            }

        except Exception as e:
            # Fallback: return the original URL
            return {
                "sources": [
                    {
                        "url": url,
                        "quality": "auto",
                        "type": "embed",
                        "error": str(e),
                        "note": "Extraction failed - returning embed URL"
                    }
                ],
                "subtitles": [],
                "embedURL": url
            }

class StreamSBExtractor(BaseExtractor):
    """
    Extractor for StreamSB servers
    """
    
    def extract(self, url: str, is_alternative: bool = False) -> List[Dict[str, Any]]:
        """Extract sources from StreamSB URL"""
        try:
            # Simplified extraction - returns list of sources
            return [
                {
                    "url": url,
                    "type": "mp4",
                    "quality": "auto"
                }
            ]
            
        except Exception as e:
            raise ExtractorError(f"StreamSB extraction failed: {str(e)}")

class StreamTapeExtractor(BaseExtractor):
    """
    Extractor for StreamTape servers
    """
    
    def extract(self, url: str) -> List[Dict[str, Any]]:
        """Extract sources from StreamTape URL"""
        try:
            # Get the page content
            response = self.session.get(url)
            response.raise_for_status()
            
            # Look for video URL in the page
            # StreamTape typically has the video URL in a specific format
            content = response.text
            
            # Try to find the video URL using regex
            # This is a simplified pattern - actual implementation would be more robust
            video_url_pattern = r'document\.getElementById\([\'"]videolink[\'"]\)\.innerHTML = [\'"]([^\'"]+)[\'"]'
            match = re.search(video_url_pattern, content)
            
            if match:
                video_url = match.group(1)
                return [
                    {
                        "url": video_url,
                        "type": "mp4",
                        "quality": "auto"
                    }
                ]
            else:
                # Fallback to original URL
                return [
                    {
                        "url": url,
                        "type": "mp4",
                        "quality": "auto"
                    }
                ]
                
        except Exception as e:
            raise ExtractorError(f"StreamTape extraction failed: {str(e)}")

class ExtractorFactory:
    """Factory class to get appropriate extractor based on URL"""
    
    @staticmethod
    def get_extractor(url: str) -> BaseExtractor:
        """Get appropriate extractor based on URL domain"""
        parsed_url = urlparse(url)
        domain = parsed_url.netloc.lower()
        
        if "megacloud" in domain or "vidstreaming" in domain:
            return MegaCloudExtractor()
        elif "rapidcloud" in domain or "vidcloud" in domain:
            return RapidCloudExtractor()
        elif "streamsb" in domain or "sbembed" in domain:
            return StreamSBExtractor()
        elif "streamtape" in domain:
            return StreamTapeExtractor()
        else:
            # Default to MegaCloud extractor
            return MegaCloudExtractor()
    
    @staticmethod
    def extract_sources(url: str, server_type: str = None) -> Dict[str, Any]:
        """
        Extract sources from URL using appropriate extractor
        
        Args:
            url: The streaming URL
            server_type: Optional server type hint
            
        Returns:
            Dictionary with extracted sources and metadata
        """
        try:
            extractor = ExtractorFactory.get_extractor(url)
            result = extractor.extract(url)
            
            # Ensure consistent return format
            if isinstance(result, list):
                # Convert list format to dict format
                return {
                    "sources": result,
                    "subtitles": [],
                    "intro": None,
                    "outro": None
                }
            else:
                return result
                
        except Exception as e:
            # Return basic fallback result
            return {
                "sources": [
                    {
                        "url": url,
                        "type": "mp4",
                        "quality": "auto"
                    }
                ],
                "subtitles": [],
                "intro": None,
                "outro": None,
                "error": str(e)
            }

# Utility functions for decryption (simplified versions)
def decrypt_source_url(encrypted_url: str, key: str = None) -> str:
    """
    Decrypt source URL - simplified version
    In production, this would implement the actual decryption logic
    """
    try:
        # This is a placeholder - actual implementation would depend on the encryption method
        if encrypted_url.startswith("http"):
            return encrypted_url
        
        # Try base64 decode as a simple example
        try:
            decoded = base64.b64decode(encrypted_url).decode('utf-8')
            if decoded.startswith("http"):
                return decoded
        except:
            pass
        
        return encrypted_url
        
    except Exception:
        return encrypted_url

def extract_m3u8_sources(m3u8_url: str) -> List[Dict[str, Any]]:
    """
    Extract different quality sources from m3u8 playlist
    """
    try:
        response = requests.get(m3u8_url, headers={"User-Agent": USER_AGENT_HEADER})
        response.raise_for_status()
        
        content = response.text
        sources = []
        
        # Parse m3u8 content for different quality streams
        lines = content.split('\n')
        current_quality = "auto"
        
        for i, line in enumerate(lines):
            line = line.strip()
            
            # Look for quality information
            if line.startswith('#EXT-X-STREAM-INF:'):
                # Extract resolution or bandwidth info
                if 'RESOLUTION=' in line:
                    resolution_match = re.search(r'RESOLUTION=(\d+x\d+)', line)
                    if resolution_match:
                        resolution = resolution_match.group(1)
                        height = resolution.split('x')[1]
                        current_quality = f"{height}p"
                
                # Next line should be the URL
                if i + 1 < len(lines):
                    next_line = lines[i + 1].strip()
                    if next_line and not next_line.startswith('#'):
                        # Make URL absolute if it's relative
                        if not next_line.startswith('http'):
                            next_line = urljoin(m3u8_url, next_line)
                        
                        sources.append({
                            "url": next_line,
                            "type": "hls",
                            "quality": current_quality
                        })
        
        # If no sources found, return the original URL
        if not sources:
            sources.append({
                "url": m3u8_url,
                "type": "hls",
                "quality": "auto"
            })
        
        return sources
        
    except Exception:
        # Return original URL as fallback
        return [
            {
                "url": m3u8_url,
                "type": "hls", 
                "quality": "auto"
            }
        ]

# Export main classes and functions
__all__ = [
    'ExtractorFactory',
    'MegaCloudExtractor',
    'RapidCloudExtractor', 
    'StreamSBExtractor',
    'StreamTapeExtractor',
    'ExtractorError',
    'decrypt_source_url',
    'extract_m3u8_sources'
]