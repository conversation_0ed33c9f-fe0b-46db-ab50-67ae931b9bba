"""
Video extractors for different streaming servers
Based on the TypeScript extractors from the aniwatch implementation
"""

import re
import json
import base64
import time
import logging
import requests
from urllib.parse import urlparse, parse_qs, urljoin
from typing import Dict, List, Optional, Any, Union
from .constants import USER_AGENT_HEADER

logger = logging.getLogger(__name__)

class ExtractorError(Exception):
    """Base exception for extractor errors"""
    pass

class BaseExtractor:
    """Base class for video extractors"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            "User-Agent": USER_AGENT_HEADER
        })
    
    def extract(self, url: str) -> Dict[str, Any]:
        """Extract video sources from URL"""
        raise NotImplementedError("Subclasses must implement extract method")

class MegaCloudExtractor(BaseExtractor):
    """
    Extractor for MegaCloud/VidStreaming servers
    Based on the TypeScript implementation's extract() method (not extract2)
    """

    def __init__(self):
        super().__init__()
        self.base_url = "https://megacloud.tv"
        self.script_url = "https://megacloud.tv/js/player/a/prod/e1-player.min.js"
        self.sources_url = "https://megacloud.tv/embed-2/ajax/e-1/getSources"

    def extract_variables(self, text: str) -> List[int]:
        """Extract variables from the JavaScript code (simplified version)"""
        try:
            # Look for common variable patterns in the minified JS
            patterns = [
                r'case\s+0x[0-9a-f]+:.*?=\s*([0-9]+)',
                r'0x[0-9a-f]+\s*:\s*([0-9]+)',
                r'=\s*([0-9]{4,})',
                r'case\s+([0-9]+):',
            ]

            variables = []
            for pattern in patterns:
                matches = re.findall(pattern, text, re.IGNORECASE)
                for match in matches:
                    try:
                        var = int(match)
                        if 1000 <= var <= 999999:  # Reasonable range
                            variables.append(var)
                    except ValueError:
                        continue

            # Remove duplicates and return first few
            unique_vars = list(dict.fromkeys(variables))
            return unique_vars[:10]  # Return first 10 unique variables

        except Exception:
            # Fallback variables based on common patterns
            return [2297, 3311, 681, 1084, 2792, 1598, 3779, 2954]

    def get_secret(self, encrypted_string: str, variables: List[int]) -> Dict[str, str]:
        """Get secret key and encrypted source"""
        try:
            # Try different combinations of variables
            for i, var in enumerate(variables[:5]):  # Try first 5 variables
                try:
                    # Convert variable to hex and use as key
                    key = str(var)

                    # Try to decrypt with this key
                    decrypted = self.decrypt(encrypted_string, key)
                    if decrypted and decrypted.startswith('['):
                        return {
                            "secret": key,
                            "encryptedSource": encrypted_string
                        }
                except:
                    continue

            # Fallback: use a common pattern
            return {
                "secret": str(variables[0]) if variables else "2297",
                "encryptedSource": encrypted_string
            }

        except Exception:
            return {
                "secret": "2297",  # Fallback
                "encryptedSource": encrypted_string
            }

    def decrypt(self, encrypted_string: str, key: str) -> str:
        """Decrypt the encrypted string using AES"""
        try:
            import base64
            from Crypto.Cipher import AES
            from Crypto.Util.Padding import unpad

            # Decode base64
            encrypted_data = base64.b64decode(encrypted_string)

            # Extract IV (first 16 bytes)
            iv = encrypted_data[:16]
            ciphertext = encrypted_data[16:]

            # Create cipher
            key_bytes = key.encode('utf-8')
            # Pad or truncate key to 32 bytes
            if len(key_bytes) < 32:
                key_bytes = key_bytes + b'0' * (32 - len(key_bytes))
            else:
                key_bytes = key_bytes[:32]

            cipher = AES.new(key_bytes, AES.MODE_CBC, iv)

            # Decrypt
            decrypted = cipher.decrypt(ciphertext)
            decrypted = unpad(decrypted, AES.block_size)

            return decrypted.decode('utf-8')

        except Exception as e:
            logger.debug(f"Decryption failed: {str(e)}")
            return ""

    def extract(self, url: str) -> Dict[str, Any]:
        """Extract sources from MegaCloud URL using the simplified method"""
        try:
            parsed_url = urlparse(url)
            video_id = parsed_url.path.split("/")[-1].split("?")[0]

            # Get sources data
            sources_response = self.session.get(
                f"{self.sources_url}?id={video_id}",
                headers={
                    "Accept": "*/*",
                    "X-Requested-With": "XMLHttpRequest",
                    "User-Agent": USER_AGENT_HEADER,
                    "Referer": url,
                },
                timeout=10
            )
            sources_response.raise_for_status()

            sources_data = sources_response.json()

            if not sources_data:
                raise Exception("No sources data received")

            # Check if sources are already decrypted
            encrypted_string = sources_data.get("sources", "")
            if not sources_data.get("encrypted", True) and isinstance(encrypted_string, list):
                # Sources are already decrypted
                return {
                    "sources": [
                        {
                            "url": s.get("file", ""),
                            "quality": s.get("type", "auto"),
                            "type": "hls" if s.get("file", "").endswith(".m3u8") else "mp4"
                        }
                        for s in encrypted_string
                    ],
                    "subtitles": [
                        {
                            "url": t.get("file", ""),
                            "lang": t.get("label", "Unknown"),
                            "id": t.get("label", "").lower()
                        }
                        for t in sources_data.get("tracks", [])
                        if t.get("kind") == "captions"
                    ],
                    "intro": sources_data.get("intro", {"start": 0, "end": 0}),
                    "embedURL": url
                }

            # Sources are encrypted, try to decrypt
            if not isinstance(encrypted_string, str):
                raise Exception("Invalid encrypted string format")

            # Get the player script to extract variables
            script_response = self.session.get(
                f"{self.script_url}?v={int(time.time())}",
                timeout=10
            )
            script_response.raise_for_status()

            script_text = script_response.text
            variables = self.extract_variables(script_text)

            if not variables:
                raise Exception("Could not extract variables from script")

            # Get secret and decrypt
            secret_data = self.get_secret(encrypted_string, variables)
            decrypted = self.decrypt(secret_data["encryptedSource"], secret_data["secret"])

            if not decrypted:
                raise Exception("Decryption failed")

            # Parse decrypted sources
            try:
                decrypted_sources = json.loads(decrypted)
                return {
                    "sources": [
                        {
                            "url": s.get("file", ""),
                            "quality": s.get("type", "auto"),
                            "type": "hls" if s.get("file", "").endswith(".m3u8") else "mp4"
                        }
                        for s in decrypted_sources
                    ],
                    "subtitles": [
                        {
                            "url": t.get("file", ""),
                            "lang": t.get("label", "Unknown"),
                            "id": t.get("label", "").lower()
                        }
                        for t in sources_data.get("tracks", [])
                        if t.get("kind") == "captions"
                    ],
                    "intro": sources_data.get("intro", {"start": 0, "end": 0}),
                    "embedURL": url
                }
            except json.JSONDecodeError:
                raise Exception("Failed to parse decrypted sources")

        except Exception as e:
            logger.warning(f"MegaCloud extraction failed: {str(e)}")
            # Return embed URL as fallback
            return {
                "sources": [
                    {
                        "url": url,
                        "quality": "embed",
                        "type": "embed",
                        "note": f"Extraction failed: {str(e)}. This is an embed URL that requires complex WASM decryption."
                    }
                ],
                "subtitles": [],
                "embedURL": url,
                "extraction_error": str(e)
            }

class RapidCloudExtractor(BaseExtractor):
    """
    Extractor for RapidCloud/VidCloud servers
    Enhanced version that attempts to extract actual video sources
    """

    def extract(self, url: str) -> Dict[str, Any]:
        """Extract sources from RapidCloud URL"""
        try:
            # Try to get the embed page content
            response = self.session.get(url, headers={
                "Referer": "https://hianime.sx/",
                "User-Agent": USER_AGENT_HEADER,
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"
            }, timeout=10)
            response.raise_for_status()

            content = response.text
            sources = []
            subtitles = []

            # Look for video sources in the page
            # RapidCloud often has sources in JavaScript variables
            source_patterns = [
                r'file:\s*["\']([^"\']+\.m3u8[^"\']*)["\']',
                r'src:\s*["\']([^"\']+\.m3u8[^"\']*)["\']',
                r'source:\s*["\']([^"\']+\.m3u8[^"\']*)["\']',
                r'(https?://[^\s"\'<>]+\.m3u8[^\s"\'<>]*)'
            ]

            for pattern in source_patterns:
                matches = re.findall(pattern, content)
                for match in matches:
                    if match.startswith('http') and '.m3u8' in match:
                        sources.append({
                            "url": match,
                            "quality": "auto",
                            "type": "hls"
                        })

            # Remove duplicates
            seen_urls = set()
            unique_sources = []
            for source in sources:
                if source["url"] not in seen_urls:
                    seen_urls.add(source["url"])
                    unique_sources.append(source)

            # If no sources found, return the embed URL
            if not unique_sources:
                unique_sources.append({
                    "url": url,
                    "quality": "auto",
                    "type": "embed",
                    "note": "Embed URL - may require additional processing"
                })

            return {
                "sources": unique_sources,
                "subtitles": subtitles,
                "embedURL": url
            }

        except Exception as e:
            # Fallback: return the original URL
            return {
                "sources": [
                    {
                        "url": url,
                        "quality": "auto",
                        "type": "embed",
                        "error": str(e),
                        "note": "Extraction failed - returning embed URL"
                    }
                ],
                "subtitles": [],
                "embedURL": url
            }

class StreamSBExtractor(BaseExtractor):
    """
    Extractor for StreamSB servers
    """
    
    def extract(self, url: str, is_alternative: bool = False) -> List[Dict[str, Any]]:
        """Extract sources from StreamSB URL"""
        try:
            # Simplified extraction - returns list of sources
            return [
                {
                    "url": url,
                    "type": "mp4",
                    "quality": "auto"
                }
            ]
            
        except Exception as e:
            raise ExtractorError(f"StreamSB extraction failed: {str(e)}")

class StreamTapeExtractor(BaseExtractor):
    """
    Extractor for StreamTape servers
    """
    
    def extract(self, url: str) -> List[Dict[str, Any]]:
        """Extract sources from StreamTape URL"""
        try:
            # Get the page content
            response = self.session.get(url)
            response.raise_for_status()
            
            # Look for video URL in the page
            # StreamTape typically has the video URL in a specific format
            content = response.text
            
            # Try to find the video URL using regex
            # This is a simplified pattern - actual implementation would be more robust
            video_url_pattern = r'document\.getElementById\([\'"]videolink[\'"]\)\.innerHTML = [\'"]([^\'"]+)[\'"]'
            match = re.search(video_url_pattern, content)
            
            if match:
                video_url = match.group(1)
                return [
                    {
                        "url": video_url,
                        "type": "mp4",
                        "quality": "auto"
                    }
                ]
            else:
                # Fallback to original URL
                return [
                    {
                        "url": url,
                        "type": "mp4",
                        "quality": "auto"
                    }
                ]
                
        except Exception as e:
            raise ExtractorError(f"StreamTape extraction failed: {str(e)}")

class ExtractorFactory:
    """Factory class to get appropriate extractor based on URL"""
    
    @staticmethod
    def get_extractor(url: str) -> BaseExtractor:
        """Get appropriate extractor based on URL domain"""
        parsed_url = urlparse(url)
        domain = parsed_url.netloc.lower()
        
        if "megacloud" in domain or "vidstreaming" in domain:
            return MegaCloudExtractor()
        elif "rapidcloud" in domain or "vidcloud" in domain:
            return RapidCloudExtractor()
        elif "streamsb" in domain or "sbembed" in domain:
            return StreamSBExtractor()
        elif "streamtape" in domain:
            return StreamTapeExtractor()
        else:
            # Default to MegaCloud extractor
            return MegaCloudExtractor()
    
    @staticmethod
    def extract_sources(url: str, server_type: str = None) -> Dict[str, Any]:
        """
        Extract sources from URL using appropriate extractor
        
        Args:
            url: The streaming URL
            server_type: Optional server type hint
            
        Returns:
            Dictionary with extracted sources and metadata
        """
        try:
            extractor = ExtractorFactory.get_extractor(url)
            result = extractor.extract(url)
            
            # Ensure consistent return format
            if isinstance(result, list):
                # Convert list format to dict format
                return {
                    "sources": result,
                    "subtitles": [],
                    "intro": None,
                    "outro": None
                }
            else:
                return result
                
        except Exception as e:
            # Return basic fallback result
            return {
                "sources": [
                    {
                        "url": url,
                        "type": "mp4",
                        "quality": "auto"
                    }
                ],
                "subtitles": [],
                "intro": None,
                "outro": None,
                "error": str(e)
            }

# Utility functions for decryption (simplified versions)
def decrypt_source_url(encrypted_url: str, key: str = None) -> str:
    """
    Decrypt source URL - simplified version
    In production, this would implement the actual decryption logic
    """
    try:
        # This is a placeholder - actual implementation would depend on the encryption method
        if encrypted_url.startswith("http"):
            return encrypted_url
        
        # Try base64 decode as a simple example
        try:
            decoded = base64.b64decode(encrypted_url).decode('utf-8')
            if decoded.startswith("http"):
                return decoded
        except:
            pass
        
        return encrypted_url
        
    except Exception:
        return encrypted_url

def extract_m3u8_sources(m3u8_url: str) -> List[Dict[str, Any]]:
    """
    Extract different quality sources from m3u8 playlist
    """
    try:
        response = requests.get(m3u8_url, headers={"User-Agent": USER_AGENT_HEADER})
        response.raise_for_status()
        
        content = response.text
        sources = []
        
        # Parse m3u8 content for different quality streams
        lines = content.split('\n')
        current_quality = "auto"
        
        for i, line in enumerate(lines):
            line = line.strip()
            
            # Look for quality information
            if line.startswith('#EXT-X-STREAM-INF:'):
                # Extract resolution or bandwidth info
                if 'RESOLUTION=' in line:
                    resolution_match = re.search(r'RESOLUTION=(\d+x\d+)', line)
                    if resolution_match:
                        resolution = resolution_match.group(1)
                        height = resolution.split('x')[1]
                        current_quality = f"{height}p"
                
                # Next line should be the URL
                if i + 1 < len(lines):
                    next_line = lines[i + 1].strip()
                    if next_line and not next_line.startswith('#'):
                        # Make URL absolute if it's relative
                        if not next_line.startswith('http'):
                            next_line = urljoin(m3u8_url, next_line)
                        
                        sources.append({
                            "url": next_line,
                            "type": "hls",
                            "quality": current_quality
                        })
        
        # If no sources found, return the original URL
        if not sources:
            sources.append({
                "url": m3u8_url,
                "type": "hls",
                "quality": "auto"
            })
        
        return sources
        
    except Exception:
        # Return original URL as fallback
        return [
            {
                "url": m3u8_url,
                "type": "hls", 
                "quality": "auto"
            }
        ]

# Export main classes and functions
__all__ = [
    'ExtractorFactory',
    'MegaCloudExtractor',
    'RapidCloudExtractor', 
    'StreamSBExtractor',
    'StreamTapeExtractor',
    'ExtractorError',
    'decrypt_source_url',
    'extract_m3u8_sources'
]