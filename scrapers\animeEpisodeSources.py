"""
Anime Episode Sources Scraper
Based on the TypeScript implementation from aniwatch/src/hianime/scrapers/animeEpisodeSrcs.ts
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Union, Any
from enum import Enum
import requests
from bs4 import BeautifulSoup
import logging
import json
import cloudscraper
from urllib.parse import urlparse, urljoin
from .constants import SRC_BASE_URL, SRC_AJAX_URL, USER_AGENT_HEADER, ACCEPT_HEADER, ACCEPT_ENCODING_HEADER
from .client import client
from .extractors import ExtractorFactory

# Configure logging
logger = logging.getLogger(__name__)

class AnimeEpisodeSourcesError(Exception):
    """Custom exception for anime episode sources errors"""
    
    def __init__(self, message: str, function_name: str = "", status_code: int = 500):
        self.message = message
        self.function_name = function_name
        self.status_code = status_code
        super().__init__(self.message)
    
    @classmethod
    def wrap_error(cls, error: Exception, function_name: str):
        """Wrap an existing error with additional context"""
        if isinstance(error, cls):
            return error
        return cls(str(error), function_name)

class Servers(Enum):
    """Anime streaming servers enum"""
    VID_STREAMING = "hd-1"
    MEGA_CLOUD = "megacloud"
    STREAM_SB = "streamsb"
    STREAM_TAPE = "streamtape"
    VID_CLOUD = "hd-2"

# Server ID mapping (from TypeScript implementation)
SERVER_ID_MAP = {
    Servers.VID_STREAMING: 4,  # vidtreaming -> 4
    Servers.VID_CLOUD: 1,      # rapidcloud -> 1
    Servers.STREAM_SB: 5,      # streamsb -> 5
    Servers.STREAM_TAPE: 3,    # streamtape -> 3
    Servers.MEGA_CLOUD: 4,     # megacloud -> 4 (same as vidstreaming)
}

@dataclass
class Video:
    """Video source data structure"""
    url: str
    quality: Optional[str] = None
    isM3U8: Optional[bool] = None
    size: Optional[int] = None

@dataclass
class Subtitle:
    """Subtitle data structure"""
    id: Optional[str] = None
    url: str = ""
    lang: str = ""

@dataclass
class Intro:
    """Intro timing data structure"""
    start: int = 0
    end: int = 0

@dataclass
class ScrapedAnimeEpisodesSources:
    """Main data structure for episode sources"""
    headers: Optional[Dict[str, str]] = None
    intro: Optional[Intro] = None
    subtitles: List[Subtitle] = field(default_factory=list)
    sources: List[Video] = field(default_factory=list)
    download: Optional[str] = None
    embedURL: Optional[str] = None
    anilistID: Optional[int] = None
    malID: Optional[int] = None

def retrieve_server_id(soup: BeautifulSoup, server_index: int, category: str) -> Optional[str]:
    """
    Retrieve server ID from the servers list HTML
    Python equivalent of the TypeScript retrieveServerId function
    """
    try:
        # Find the server item with matching data-server-id
        selector = f".ps_-block.ps_-block-sub.servers-{category} > .ps__-list .server-item"
        server_items = soup.select(selector)
        
        for item in server_items:
            if item.get("data-server-id") == str(server_index):
                return item.get("data-id")
        
        return None
    except Exception as e:
        logger.error(f"Error retrieving server ID: {str(e)}")
        return None

def _get_anime_episode_sources(
    episode_id: str,
    server: Servers = Servers.VID_STREAMING,
    category: str = "sub"
) -> ScrapedAnimeEpisodesSources:
    """
    Internal function to get anime episode sources
    Python equivalent of the TypeScript _getAnimeEpisodeSources function
    """
    # If episode_id is already a direct URL, extract from it
    if episode_id.startswith("http"):
        server_url = urlparse(episode_id)
        
        # Use appropriate extractor based on server type
        try:
            extracted_data = ExtractorFactory.extract_sources(episode_id, server.value)
            
            result = ScrapedAnimeEpisodesSources()
            result.sources = [Video(url=src["url"], quality=src.get("quality")) for src in extracted_data.get("sources", [])]
            result.subtitles = [Subtitle(url=sub["url"], lang=sub.get("lang", "")) for sub in extracted_data.get("subtitles", [])]
            result.headers = {"Referer": f"{server_url.scheme}://{server_url.netloc}/"}
            
            if "intro" in extracted_data and extracted_data["intro"]:
                result.intro = Intro(
                    start=extracted_data["intro"].get("start", 0),
                    end=extracted_data["intro"].get("end", 0)
                )
            
            return result
            
        except Exception as e:
            logger.error(f"Error extracting from URL {episode_id}: {str(e)}")
            raise AnimeEpisodeSourcesError(f"Failed to extract sources: {str(e)}", "_get_anime_episode_sources")
    
    # Construct episode URL
    ep_id = urljoin(SRC_BASE_URL, f"/watch/{episode_id}")
    logger.info(f"EPISODE_ID: {ep_id}")
    
    try:
        # Get episode servers
        episode_number = ep_id.split("?ep=")[1] if "?ep=" in ep_id else ""
        if not episode_number:
            raise AnimeEpisodeSourcesError("Invalid episode ID format", "_get_anime_episode_sources", 400)
        
        servers_url = f"{SRC_AJAX_URL}/v2/episode/servers?episodeId={episode_number}"
        
        response = client.get(servers_url, headers={
            "Referer": ep_id,
            "X-Requested-With": "XMLHttpRequest",
        }, timeout=10)
        response.raise_for_status()
        
        servers_data = response.json()
        soup = BeautifulSoup(servers_data.get("html", ""), 'html.parser')
        
        # Get server ID based on server type and category
        server_index = SERVER_ID_MAP.get(server)
        if server_index is None:
            raise AnimeEpisodeSourcesError(f"Unsupported server: {server.value}", "_get_anime_episode_sources", 400)
        
        server_id = retrieve_server_id(soup, server_index, category)
        if not server_id:
            raise AnimeEpisodeSourcesError(f"Server {server.value} not found for category {category}", "_get_anime_episode_sources", 500)
        
        logger.info(f"SERVER_ID: {server_id}")
        
        # Get streaming link
        sources_url = f"{SRC_AJAX_URL}/v2/episode/sources?id={server_id}"
        sources_response = client.get(sources_url, timeout=10)
        sources_response.raise_for_status()
        
        sources_data = sources_response.json()
        streaming_link = sources_data.get("link")
        
        if not streaming_link:
            raise AnimeEpisodeSourcesError("No streaming link found", "_get_anime_episode_sources", 404)
        
        logger.info(f"THE LINK: {streaming_link}")
        
        # Recursively call with the streaming link
        return _get_anime_episode_sources(streaming_link, server, category)
        
    except requests.RequestException as e:
        raise AnimeEpisodeSourcesError(f"Network error: {str(e)}", "_get_anime_episode_sources")
    except Exception as e:
        raise AnimeEpisodeSourcesError.wrap_error(e, "_get_anime_episode_sources")

def get_anime_episode_sources(
    episode_id: str,
    server: str = "hd-1",
    category: str = "sub"
) -> Dict[str, Any]:
    """
    Get anime episode sources with additional metadata
    Python equivalent of the TypeScript getAnimeEpisodeSources function
    
    Args:
        episode_id: Episode ID in format 'anime-name-id?ep=episode_number'
        server: Server type (hd-1, megacloud, streamsb, streamtape, hd-2)
        category: Episode category (sub, dub, raw)
    
    Returns:
        Dictionary containing sources, headers, and metadata
    """
    try:
        # Validate inputs
        if not episode_id or "?ep=" not in episode_id:
            raise AnimeEpisodeSourcesError("Invalid anime episode id", "get_anime_episode_sources", 400)
        
        if not category.strip():
            raise AnimeEpisodeSourcesError("Invalid anime episode category", "get_anime_episode_sources", 400)
        
        # Convert server string to enum
        try:
            server_enum = Servers(server)
        except ValueError:
            raise AnimeEpisodeSourcesError(f"Invalid server: {server}", "get_anime_episode_sources", 400)
        
        # Extract anime URL for metadata
        anime_url = urljoin(SRC_BASE_URL, episode_id.split("?ep=")[0])
        
        # Get episode sources and anime metadata in parallel
        episode_src_data = _get_anime_episode_sources(episode_id, server_enum, category)
        
        # Try to get anime metadata for anilist and mal IDs
        mal_id = None
        anilist_id = None
        
        try:
            anime_response = client.get(anime_url, headers={
                "Referer": SRC_BASE_URL,
                "User-Agent": USER_AGENT_HEADER,
                "X-Requested-With": "XMLHttpRequest",
            }, timeout=10)
            anime_response.raise_for_status()
            
            anime_soup = BeautifulSoup(anime_response.text, 'html.parser')
            sync_data = anime_soup.find("script", id="syncData")
            
            if sync_data and sync_data.text:
                try:
                    sync_json = json.loads(sync_data.text)
                    anilist_id = int(sync_json.get("anilist_id", 0)) or None
                    mal_id = int(sync_json.get("mal_id", 0)) or None
                except (json.JSONDecodeError, ValueError):
                    logger.warning("Failed to parse sync data")
                    
        except Exception as e:
            logger.warning(f"Failed to get anime metadata: {str(e)}")
        
        # Set metadata
        episode_src_data.anilistID = anilist_id
        episode_src_data.malID = mal_id
        
        # Convert to dictionary format
        return {
            "success": True,
            "data": {
                "headers": episode_src_data.headers or {},
                "sources": [
                    {
                        "url": video.url,
                        "quality": video.quality,
                        "isM3U8": video.isM3U8,
                        "size": video.size
                    }
                    for video in episode_src_data.sources
                ],
                "subtitles": [
                    {
                        "id": sub.id,
                        "url": sub.url,
                        "lang": sub.lang
                    }
                    for sub in episode_src_data.subtitles
                ],
                "intro": {
                    "start": episode_src_data.intro.start,
                    "end": episode_src_data.intro.end
                } if episode_src_data.intro else None,
                "download": episode_src_data.download,
                "embedURL": episode_src_data.embedURL,
                "anilistID": episode_src_data.anilistID,
                "malID": episode_src_data.malID
            }
        }
        
    except AnimeEpisodeSourcesError:
        raise
    except Exception as e:
        raise AnimeEpisodeSourcesError.wrap_error(e, "get_anime_episode_sources")
