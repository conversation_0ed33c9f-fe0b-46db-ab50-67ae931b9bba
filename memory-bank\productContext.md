# Product Context

## Project Purpose
The project aims to create a robust anime information scraper that can reliably extract comprehensive data from anime websites while handling various technical challenges like Cloudflare protection and rate limiting.

## Problems Solved
1. Data Extraction
   - Reliable anime information gathering
   - Structured data organization
   - Comprehensive content coverage
   - Error handling and recovery

2. Technical Challenges
   - Cloudflare protection bypass
   - Rate limiting management
   - Session handling
   - Error recovery

3. User Experience
   - Reliable data extraction
   - Clear error messages
   - Debug information
   - Structured output

## How It Works
1. Request Flow
   ```
   User Request → Cloudflare Bypass → Data Extraction → Structured Output
   ```

2. Data Extraction
   - Basic anime information
   - Episode details
   - Genres and studios
   - Seasons information
   - Character details
   - Promotional content
   - Recommendations

3. Error Handling
   - Detailed error messages
   - Debug information
   - Recovery mechanisms
   - Graceful degradation

## User Experience Goals
1. Reliability
   - Consistent data extraction
   - Error recovery
   - Session management
   - Rate limiting

2. Clarity
   - Clear error messages
   - Debug information
   - Structured output
   - Documentation

3. Performance
   - Fast response times
   - Resource efficiency
   - Memory management
   - File I/O optimization

4. Maintainability
   - Clear code structure
   - Documentation
   - Error handling
   - Debug tools

## Success Metrics
1. Data Quality
   - Extraction accuracy
   - Data completeness
   - Error rate
   - Recovery success

2. Performance
   - Response time
   - Resource usage
   - Memory efficiency
   - File I/O speed

3. Reliability
   - Success rate
   - Error recovery
   - Session stability
   - Rate limit compliance

4. Maintainability
   - Code clarity
   - Documentation
   - Debug capability
   - Error tracking

## Future Goals
1. Enhanced Features
   - Rate limiting
   - Caching system
   - Proxy support
   - Retry mechanism

2. Improved Reliability
   - Better error handling
   - Enhanced recovery
   - Session management
   - Rate limit compliance

3. Better Performance
   - Response optimization
   - Resource management
   - Memory efficiency
   - File I/O speed

4. Enhanced User Experience
   - Better error messages
   - More debug information
   - Clearer documentation
   - Better structure

## User Requirements
1. Data Needs
   - Comprehensive information
   - Accurate extraction
   - Structured output
   - Error handling

2. Technical Requirements
   - Cloudflare bypass
   - Rate limiting
   - Session management
   - Error recovery

3. Performance Requirements
   - Fast response
   - Resource efficiency
   - Memory management
   - File I/O speed

4. Maintenance Requirements
   - Clear code
   - Documentation
   - Debug tools
   - Error tracking 