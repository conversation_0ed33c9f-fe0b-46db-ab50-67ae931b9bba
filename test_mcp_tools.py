#!/usr/bin/env python3
"""
Test MCP tools functionality
"""

import asyncio
import logging
from mcp.server.fastmcp import Context

logging.basicConfig(level=logging.INFO)

async def test_get_available_servers():
    """Test the get_available_servers MCP tool"""
    print("🧪 Testing get_available_servers...")
    
    try:
        # Import the function from main
        from main import get_available_servers
        
        # Create a mock context
        ctx = Context()
        
        # Call the function
        result = await get_available_servers(ctx)
        
        print(f"✅ Function called successfully")
        print(f"📊 Success: {result.get('success', False)}")
        
        if result.get("success"):
            servers = result.get("servers", [])
            categories = result.get("categories", [])
            
            print(f"📊 Found {len(servers)} servers:")
            for server in servers:
                print(f"   - {server.get('id')}: {server.get('name')} - {server.get('description')}")
            
            print(f"📊 Found {len(categories)} categories:")
            for category in categories:
                print(f"   - {category.get('id')}: {category.get('name')} - {category.get('description')}")
        else:
            print(f"❌ Error: {result.get('error', 'Unknown error')}")
        
        return result.get("success", False)
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

async def test_get_anime_episode_sources_validation():
    """Test the get_anime_episode_sources MCP tool with validation"""
    print("🧪 Testing get_anime_episode_sources validation...")
    
    try:
        from main import get_anime_episode_sources
        
        ctx = Context()
        
        # Test missing episode_id
        result = await get_anime_episode_sources(ctx)
        print(f"✅ Missing episode_id: Success={result.get('success', False)}, Error={result.get('error', 'None')}")
        
        # Test invalid episode_id
        result = await get_anime_episode_sources(ctx, episode_id="invalid")
        print(f"✅ Invalid episode_id: Success={result.get('success', False)}, Error={result.get('error', 'None')}")
        
        # Test invalid server
        result = await get_anime_episode_sources(ctx, episode_id="test?ep=1", server="invalid")
        print(f"✅ Invalid server: Success={result.get('success', False)}, Error={result.get('error', 'None')}")
        
        # Test invalid category
        result = await get_anime_episode_sources(ctx, episode_id="test?ep=1", category="invalid")
        print(f"✅ Invalid category: Success={result.get('success', False)}, Error={result.get('error', 'None')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

async def test_mcp_server_creation():
    """Test that the MCP server can be created"""
    print("🧪 Testing MCP server creation...")
    
    try:
        from main import mcp
        
        print(f"✅ MCP server created: {type(mcp)}")
        
        # Check if tools are registered
        tools = mcp._tools if hasattr(mcp, '_tools') else {}
        print(f"📊 Registered tools: {len(tools)}")
        
        expected_tools = [
            'get_home_page',
            'get_trending_anime', 
            'get_anime_genres',
            'get_anime_recommendations',
            'get_anime_about_info',
            'get_anime_episode_sources',
            'get_available_servers'
        ]
        
        for tool_name in expected_tools:
            if tool_name in tools:
                print(f"   ✅ {tool_name}")
            else:
                print(f"   ❌ {tool_name} (missing)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

async def main():
    """Run all tests"""
    print("🎬 MCP Tools Test Suite")
    print("=" * 60)
    
    tests = [
        test_get_available_servers,
        test_get_anime_episode_sources_validation,
        test_mcp_server_creation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        print()
        try:
            if await test():
                passed += 1
        except Exception as e:
            print(f"❌ Test failed with exception: {str(e)}")
        print("-" * 40)
    
    print(f"\n🏁 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All MCP tests passed!")
    else:
        print("⚠️  Some tests failed. Check the output above.")

if __name__ == "__main__":
    asyncio.run(main())
