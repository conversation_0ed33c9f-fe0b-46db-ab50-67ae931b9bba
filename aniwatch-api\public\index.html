<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>Aniwatch API</title>
    <meta name="content-language" content="en">
    <!-- <meta http-equiv="Content-Security-Policy"
    content="default-src 'self' https://api-aniwatch.onrender.com; style-src 'unsafe-inline'; img-src 'self' https://raw.githubusercontent.com https://img.shields.io https://contrib.rocks"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="title" content="Aniwatch API">
    <meta name="theme-color" content="#d5b3ff">
    <meta name="color-scheme" content="dark">
    <link rel="canonical" href="https://api-aniwatch.onrender.com">
    <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1">
    <meta property="og:image"
        content="https://raw.githubusercontent.com/ghoshRitesh12/aniwatch-api/refs/heads/main/public/img/hianime_v2.png">
    <meta property="og:url" content="https://api-aniwatch.onrender.com">
    <meta property="og:image:width" content="500">
    <meta property="og:image:height" content="500">
    <meta property="og:site_name" content="aniwatch">
    <meta property="og:type" content="website">
    <meta name="twitter:card" content="summary_image">
    <meta name="twitter:site" content="@aniwatch-api">
    <meta name="twitter:title" content="Aniwatch API">
    <meta name="twitter:description" content="NodeJS API for obtaining anime data from hianimez.to">
    <meta name="twitter:image:src"
        content="https://raw.githubusercontent.com/ghoshRitesh12/aniwatch-api/refs/heads/main/public/img/hianime_v2.png">
    <meta name="keywords" content="hianime api scraper anime aniwatch node express typescript">
    <meta property="og:title" content="Aniwatch API">
    <meta name="description" content="NodeJS API for obtaining anime data from hianimez.to">
    <meta property="og:description" content="NodeJS API for obtaining anime data from hianimez.to">
    <link rel="shortcut icon"
        href="https://raw.githubusercontent.com/ghoshRitesh12/aniwatch-api/refs/heads/main/public/img/hianime_v2.png">

    <style>
        * {
            box-sizing: border-box;
            font-family: sans-serif;
            transition:
                0.15s ease scale,
                0.15s ease width,
                0.15s ease height,
                0.15s ease gap;
        }

        html {
            --accent: #d5b3ff;

            height: 100%;
            color-scheme: dark;
        }

        body {
            display: flex;
            flex-direction: column;
            font-family: sans-serif;
            background-color: #151518;
            padding: 0rem 1rem 0 1rem;
            min-height: 98vh;
        }

        section {
            margin-block: auto;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: start;
            position: relative;
            isolation: isolate;
        }

        section::before {
            content: "";
            width: 100%;
            height: 100%;

            position: absolute;
            inset: -2rem 0 0 0;
            isolation: isolate;
            z-index: -1;

            background-color: #7300ff;
            opacity: .35;
            border-radius: 50%;
            filter: blur(12rem);
        }

        @media (min-width: 640px) {
            section::before {
                background-color: #7300ffbb;
                opacity: .3;
            }
        }

        .overlay {
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            overflow: hidden;

            width: 100%;
            min-height: 100%;
            isolation: isolate;
            z-index: -1;

        }

        .overlay .child {
            background-position: center center;
            background-size: cover;
            position: absolute;
            inset: 0;

            opacity: .4;
            filter: blur(5rem);
            background-image: url(https://raw.githubusercontent.com/ghoshRitesh12/aniwatch-api/refs/heads/main/public/img/wano_kuni.png);
        }

        @media (min-width: 640px) {
            .overlay .child {
                opacity: .35;
                filter: blur(8rem);
            }
        }

        .overlay::before {
            content: "";
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 2;

            background: url("https://raw.githubusercontent.com/ghoshRitesh12/aniwatch-api/refs/heads/main/public/img/dot.png") repeat;
        }

        h2 {
            color: var(--accent);
            margin-top: 0;
            font-size: 1.65rem;
            font-weight: 600;
            text-align: center;
            text-wrap: balance;
        }

        a {
            line-height: 1.3;
            text-wrap: balance;
            color: var(--accent);
        }

        img {
            object-fit: cover;
            max-width: 100%;
            font-size: .9rem;
        }

        #anime_girl {
            width: 100%;
            max-width: 10rem;
        }

        @media (min-width: 640px) {
            #anime_girl {
                max-width: 12rem;
            }
        }

        .img-wrapper {
            aspect-ratio: 1/1;
            max-width: 12rem;
            border-radius: 1rem;
            overflow: hidden;
            width: fit-content;
            margin: 1rem auto 1.5rem auto;
        }

        .api-stats-container {
            display: flex;
            gap: .2rem .5rem;
            align-items: center;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 1rem;
        }

        .api-stats-container a {
            flex-shrink: 0;
            user-select: none;
        }

        .contributors {
            margin-top: 1rem;
            text-align: center;
        }
    </style>
</head>

<!--
IMPORTANT:
    1.  This API is just an unofficial API for hianimez.to and 
        is in no other way officially related to the same.
    2.  The content that this API provides is not mine, nor is 
        it hosted by me. These belong to their respective 
        owners. This API just demonstrates how to build an API
        that scrapes websites and uses their content.
-->

<body>
    <span class="overlay">
        <span class="child"></span>
    </span>

    <section>
        <h2>
            Welcome to the Aniwatch API
            <span style="-webkit-text-fill-color: var(--accent)">⚔️</span>
        </h2>

        <div class="api-stats-container">
            <a href="https://github.com/ghoshRitesh12/aniwatch-api/stargazers">
                <img src="https://img.shields.io/github/stars/ghoshRitesh12/aniwatch-api?style=social" alt="Stars"
                    decoding="async">
            </a>
            <a href="https://github.com/ghoshRitesh12/aniwatch-api/network/members">
                <img src="https://img.shields.io/github/forks/ghoshRitesh12/aniwatch-api?style=social" alt="Forks"
                    decoding="async">
            </a>
            <a href="https://github.com/ghoshRitesh12/aniwatch-api/issues?q=is%3Aissue+is%3Aopen+">
                <img src="https://img.shields.io/github/issues/ghoshRitesh12/aniwatch-api?style=social&logo=github"
                    alt="Issues" decoding="async">
            </a>
            <a href="https://github.com/ghoshRitesh12/aniwatch-api/releases/latest">
                <img src="https://img.shields.io/github/v/release/ghoshRitesh12/aniwatch-api?display_name=release&style=social&logo=github"
                    alt="Version" decoding="async">
            </a>
        </div>

        <div style="text-align: center;">
            <div class="img-wrapper">
                <img draggable="false" id="anime_girl"
                    src="https://raw.githubusercontent.com/ghoshRitesh12/aniwatch-api/refs/heads/main/public/img/hianime_v2.png"
                    alt="kawai_anime_girl" decoding="async" fetchpriority="high" style="user-select: none;" />
            </div>

            <div style="display: flex; align-items: center; gap: .5rem; text-underline-offset: 3px;">
                <a href="https://github.com/ghoshRitesh12/aniwatch-api/releases/latest" rel="noopener noreferer">
                    Checkout latest changelogs
                </a>

                <span style="font-size: .9rem;">•</span>

                <a href="https://github.com/ghoshRitesh12/aniwatch-api#documentation" rel="noopener noreferer">
                    Visit docs for more information
                </a>
            </div>
        </div>

        <div class="contributors">
            <p style="text-wrap: balance; line-height: 1.2;">Thanks to the following people for keeping this
                project alive and
                relevant.</p>

            <a href="https://github.com/ghoshRitesh12/aniwatch-api/graphs/contributors"
                style="display: block; margin-bottom: 1rem; user-select: none;">
                <img src="https://contrib.rocks/image?repo=ghoshRitesh12/aniwatch-api" alt="Contributors"
                    decoding="async">
            </a>
        </div>
    </section>
</body>

</html>