# MegaCloud URL Extraction - Practical Solutions

This guide explains why you're seeing "We're Sorry!" pages and provides practical solutions for extracting actual video URLs from MegaCloud embed links.

## The Problem

When you get URLs like `https://megacloud.blog/embed-2/v2/e-1/NxSK1hC8AaR5?k=1`, these are **embed URLs** that require complex decryption to extract the actual video sources. The "We're Sorry!" page is anti-bot protection.

## Why the TypeScript Version Works

The aniwatch TypeScript implementation uses:

```typescript
// From aniwatch/src/extractors/megacloud.getsrcs.ts
- WASM binary execution for decryption
- Fake browser environment simulation  
- PNG image processing for key extraction
- Multi-layer AES encryption/decryption
- Dynamic JavaScript execution
- Real-time anti-bot evasion
```

This level of complexity cannot be easily replicated in Python without significant infrastructure.

## Solution 1: Use the TypeScript API as a Service

### Option A: Run aniwatch-api as a microservice

```bash
# In the aniwatch-api folder
npm install
npm start

# Then call from Python
import requests
response = requests.get('http://localhost:4000/api/v2/hianime/episode/sources', {
    'params': {
        'animeEpisodeId': 'attack-on-titan-112?ep=3304',
        'server': 'hd-1',
        'category': 'sub'
    }
})
```

### Option B: Use the aniwatch npm package

```javascript
// Node.js service
const { HiAnime } = require('aniwatch');

const hianime = new HiAnime.Scraper();
const sources = await hianime.getEpisodeSources(
    'attack-on-titan-112?ep=3304',
    'hd-1',
    'sub'
);
```

## Solution 2: Browser Automation

Use Selenium or Playwright to execute the JavaScript:

```python
from selenium import webdriver
from selenium.webdriver.common.by import By
import time
import json

def extract_with_browser(embed_url):
    driver = webdriver.Chrome()
    try:
        driver.get(embed_url)
        time.sleep(5)  # Wait for JavaScript execution
        
        # Look for video sources in the page
        scripts = driver.find_elements(By.TAG_NAME, 'script')
        for script in scripts:
            content = script.get_attribute('innerHTML')
            if 'sources' in content and '.m3u8' in content:
                # Extract sources from JavaScript
                # This requires parsing the specific format
                pass
                
        return sources
    finally:
        driver.quit()
```

## Solution 3: Hybrid Python + Node.js

Create a Node.js helper service:

```javascript
// megacloud-extractor.js
const { HiAnime } = require('aniwatch');

process.stdin.on('data', async (data) => {
    const { episodeId, server, category } = JSON.parse(data.toString());
    
    try {
        const hianime = new HiAnime.Scraper();
        const sources = await hianime.getEpisodeSources(episodeId, server, category);
        console.log(JSON.stringify(sources));
    } catch (error) {
        console.log(JSON.stringify({ error: error.message }));
    }
});
```

```python
# In your Python code
import subprocess
import json

def extract_with_nodejs(episode_id, server, category):
    process = subprocess.Popen(
        ['node', 'megacloud-extractor.js'],
        stdin=subprocess.PIPE,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE
    )
    
    input_data = json.dumps({
        'episodeId': episode_id,
        'server': server,
        'category': category
    })
    
    stdout, stderr = process.communicate(input_data.encode())
    return json.loads(stdout.decode())
```

## Solution 4: Use Embed URLs Directly

Many video players can handle embed URLs directly:

```python
def use_embed_url(embed_url):
    """
    Some video players (like VLC, mpv) can handle embed URLs
    or you can use them in iframe elements
    """
    return {
        'type': 'embed',
        'url': embed_url,
        'headers': {
            'Referer': 'https://hianime.sx/',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        },
        'note': 'Use in video player or iframe'
    }
```

## Solution 5: Alternative Servers

Try different servers that might be easier to extract:

```python
# Try different servers in order of preference
servers = ['hd-2', 'streamsb', 'streamtape', 'hd-1', 'megacloud']

for server in servers:
    try:
        result = get_anime_episode_sources(episode_id, server, category)
        if result['success'] and result['data']['sources']:
            # Check if we got actual video URLs vs embed URLs
            sources = result['data']['sources']
            for source in sources:
                if source['url'].endswith('.m3u8') or source['url'].endswith('.mp4'):
                    return result  # Found direct video URL
    except:
        continue

# If no direct URLs found, return the best embed URL
```

## Recommended Approach

For most use cases, I recommend **Solution 1** (using the TypeScript API as a microservice):

1. **Reliability**: Uses the proven TypeScript implementation
2. **Maintenance**: Automatically gets updates and fixes
3. **Performance**: Optimized for the specific decryption requirements
4. **Completeness**: Handles all edge cases and anti-bot measures

## Current Python Implementation

The current Python implementation provides:
- ✅ Correct API structure and error handling
- ✅ Server detection and routing
- ✅ Embed URL extraction with proper metadata
- ✅ Integration with MCP tools framework
- ⚠️ Returns embed URLs that require additional processing

This gives you a solid foundation that can be enhanced with any of the above solutions based on your specific needs.

## Example Integration

```python
# Enhanced version that tries multiple approaches
async def get_anime_episode_sources_enhanced(episode_id, server="hd-1", category="sub"):
    # Try the Python implementation first
    result = await get_anime_episode_sources(episode_id, server, category)
    
    if result['success']:
        sources = result['data']['sources']
        
        # Check if we got direct video URLs
        for source in sources:
            if any(source['url'].endswith(ext) for ext in ['.m3u8', '.mp4']):
                return result  # Direct video URL found
        
        # If only embed URLs, try to enhance them
        if sources and 'embed' in sources[0].get('type', ''):
            # Option 1: Try Node.js extraction
            try:
                enhanced = extract_with_nodejs(episode_id, server, category)
                if enhanced and not enhanced.get('error'):
                    return enhanced
            except:
                pass
            
            # Option 2: Return embed URL with usage instructions
            result['data']['usage_note'] = (
                "This is an embed URL. Use with video players that support "
                "embed URLs or integrate with the TypeScript aniwatch-api "
                "for full decryption."
            )
    
    return result
```

This approach gives you maximum flexibility while maintaining compatibility with the existing MCP tools framework.
