#!/usr/bin/env python3
"""
Simple function to get streaming URLs from anime episode IDs
Usage: python get_anime_url.py "the-children-of-shiunji-family-19556?ep=136211"
"""

import sys
import os

# Add the current directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def get_anime_streaming_url(episode_id, server="vidstreaming", category="sub"):
    """
    Get direct streaming URL from anime episode ID
    
    Args:
        episode_id (str): Episode ID like "the-children-of-shiunji-family-19556?ep=136211"
        server (str): Server to use ("vidstreaming", "vidcloud", "streamsb", "streamtape")
        category (str): Category ("sub", "dub", "raw")
    
    Returns:
        dict: {
            "success": bool,
            "url": str,
            "headers": dict,
            "quality": str,
            "type": str,
            "metadata": dict,
            "error": str (if failed)
        }
    """
    try:
        from scrapers.animeEpisodeSources import get_anime_episode_sources
        
        # Get episode sources
        result = get_anime_episode_sources(episode_id, server, category)
        
        if result and result.get("sources"):
            source = result["sources"][0]  # Get first source
            
            return {
                "success": True,
                "url": source.get("url"),
                "headers": result.get("headers", {}),
                "quality": source.get("quality", "auto"),
                "type": source.get("type", "mp4"),
                "download_url": result.get("download"),
                "embed_url": result.get("embedURL"),
                "metadata": {
                    "anilist_id": result.get("anilistID"),
                    "mal_id": result.get("malID"),
                    "episode_id": episode_id,
                    "server": server,
                    "category": category
                }
            }
        else:
            return {
                "success": False,
                "error": "No sources found",
                "episode_id": episode_id,
                "server": server,
                "category": category
            }
            
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "episode_id": episode_id,
            "server": server,
            "category": category
        }

def get_multiple_servers(episode_id, category="sub"):
    """
    Try multiple servers and return the first working one
    
    Args:
        episode_id (str): Episode ID
        category (str): Category ("sub", "dub", "raw")
    
    Returns:
        dict: Result from first working server
    """
    servers = ["vidstreaming", "vidcloud", "streamsb", "streamtape"]
    
    for server in servers:
        result = get_anime_streaming_url(episode_id, server, category)
        if result.get("success"):
            result["tried_servers"] = [server]
            return result
    
    return {
        "success": False,
        "error": "No working servers found",
        "tried_servers": servers,
        "episode_id": episode_id,
        "category": category
    }

def main():
    """Command line interface"""
    if len(sys.argv) < 2:
        print("Usage: python get_anime_url.py <episode_id> [server] [category]")
        print("Example: python get_anime_url.py 'the-children-of-shiunji-family-19556?ep=136211'")
        print("Example: python get_anime_url.py 'attack-on-titan-112?ep=1' vidcloud sub")
        return
    
    episode_id = sys.argv[1]
    server = sys.argv[2] if len(sys.argv) > 2 else "vidstreaming"
    category = sys.argv[3] if len(sys.argv) > 3 else "sub"
    
    print(f"🎬 Getting streaming URL for: {episode_id}")
    print(f"🖥️  Server: {server}")
    print(f"📂 Category: {category}")
    print("-" * 60)
    
    result = get_anime_streaming_url(episode_id, server, category)
    
    if result.get("success"):
        print("✅ SUCCESS!")
        print(f"📊 URL: {result['url']}")
        print(f"🎬 Quality: {result['quality']}")
        print(f"📝 Type: {result['type']}")
        print(f"🔗 Headers: {result['headers']}")
        
        if result.get("download_url"):
            print(f"📥 Download: {result['download_url']}")
        
        metadata = result.get("metadata", {})
        if metadata.get("anilist_id"):
            print(f"🆔 AniList: {metadata['anilist_id']}")
        if metadata.get("mal_id"):
            print(f"🆔 MAL: {metadata['mal_id']}")
        
        print(f"\n💡 Usage in Python:")
        print(f"import requests")
        print(f"headers = {result['headers']}")
        print(f"response = requests.get('{result['url']}', headers=headers)")
        
    else:
        print(f"❌ FAILED: {result.get('error')}")
        
        # Try multiple servers if single server failed
        print(f"\n🔄 Trying multiple servers...")
        multi_result = get_multiple_servers(episode_id, category)
        
        if multi_result.get("success"):
            print(f"✅ Found working server!")
            print(f"📊 URL: {multi_result['url']}")
            print(f"🖥️  Working server: {multi_result['metadata']['server']}")
        else:
            print(f"❌ All servers failed: {multi_result.get('error')}")

if __name__ == "__main__":
    main()
