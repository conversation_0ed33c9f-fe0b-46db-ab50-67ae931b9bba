{"name": "aniwatch-api", "version": "2.15.0", "description": "NodeJS API for obtaining anime data from hianimez.to", "main": "dist/src/server.js", "type": "module", "scripts": {"start": "node dist/src/server.js", "dev": "tsx watch src/server.ts", "build": "tsc", "buildnstart": "tsc && node dist/src/server.js", "vercel-build": "echo hello", "prepare": "node .husky/install.mjs", "test": "vitest run --config vitest.config.ts", "healthcheck": "curl -f http://localhost:4000/health", "typecheck": "tsc --noEmit", "format": "prettier --cache --write .", "format:check": "prettier --cache --check ."}, "repository": {"type": "git", "url": "git+https://github.com/ghoshRitesh12/aniwatch-api.git"}, "bugs": {"url": "https://github.com/ghoshRitesh12/aniwatch-api/issues"}, "homepage": "https://github.com/ghoshRitesh12/aniwatch-api#readme", "keywords": ["anime", "weeb", "scraper", "zoro.to", "aniwatch.to", "hianime.to", "hianimez.to"], "author": "https://github.com/ghoshRitesh12", "license": "MIT", "dependencies": {"@hono/node-server": "^1.14.2", "aniwatch": "^2.23.0", "dotenv": "^16.5.0", "envalid": "^8.0.0", "hono": "^4.7.10", "hono-rate-limiter": "^0.4.2", "ioredis": "^5.6.1", "pino": "^9.7.0"}, "devDependencies": {"@types/node": "^22.15.21", "husky": "^9.1.7", "pino-pretty": "^13.0.0", "prettier": "^3.5.3", "tsx": "^4.19.4", "typescript": "^5.8.3", "vitest": "^3.1.4"}}