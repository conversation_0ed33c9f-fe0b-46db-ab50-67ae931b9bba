#!/usr/bin/env python3
"""
Test the enhanced MegaCloud extractor
"""

import logging
from scrapers.animeEpisodeSources import get_anime_episode_sources

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_enhanced_megacloud():
    """Test the enhanced MegaCloud extractor with the user's specific episode"""
    
    print("🎬 Testing Enhanced MegaCloud Extractor")
    print("=" * 60)
    
    # The user's specific episode ID that was giving "We're Sorry!" pages
    episode_id = "the-children-of-shiunji-family-19556?ep=136211&ep=136211"
    
    print(f"📺 Episode ID: {episode_id}")
    print(f"🔧 Server: hd-1 (VidStreaming/MegaCloud)")
    print(f"📂 Category: sub")
    print()
    
    try:
        print("🚀 Calling get_anime_episode_sources...")
        result = get_anime_episode_sources(
            episode_id=episode_id,
            server="hd-1",
            category="sub"
        )
        
        if result.get("success"):
            data = result.get("data", {})
            sources = data.get("sources", [])
            headers = data.get("headers", {})
            
            print(f"✅ SUCCESS!")
            print(f"📊 Found {len(sources)} source(s)")
            print()
            
            for i, source in enumerate(sources, 1):
                url = source.get('url', 'N/A')
                quality = source.get('quality', 'N/A')
                source_type = source.get('type', 'unknown')
                note = source.get('note', '')
                error = source.get('error', '')
                
                print(f"   📹 Source {i}:")
                print(f"      Type: {source_type}")
                print(f"      Quality: {quality}")
                print(f"      URL: {url[:100]}{'...' if len(url) > 100 else ''}")
                
                if note:
                    print(f"      📝 Note: {note}")
                if error:
                    print(f"      ⚠️  Error: {error}")
                
                # Analyze the URL
                if url.endswith('.m3u8'):
                    print(f"      🎯 DIRECT HLS STREAM FOUND!")
                    print(f"      🎉 This is a real video URL, not an embed!")
                elif url.endswith('.mp4'):
                    print(f"      🎯 DIRECT MP4 VIDEO FOUND!")
                    print(f"      🎉 This is a real video URL, not an embed!")
                elif 'megacloud' in url.lower() and 'embed' in url.lower():
                    print(f"      📺 MegaCloud embed URL")
                    if 'extraction_error' in data:
                        print(f"      ⚠️  Extraction failed: {data['extraction_error']}")
                        print(f"      💡 This means the enhanced extractor tried but couldn't decrypt")
                    else:
                        print(f"      ⚠️  This is still an embed URL requiring decryption")
                else:
                    print(f"      ❓ Unknown URL type")
                
                print()
            
            # Show headers
            if headers:
                print(f"🔗 Headers ({len(headers)} total):")
                for key, value in headers.items():
                    print(f"   {key}: {value}")
                print()
            
            # Show metadata
            if data.get("anilistID"):
                print(f"📺 AniList ID: {data['anilistID']}")
            if data.get("malID"):
                print(f"📺 MAL ID: {data['malID']}")
            if data.get("embedURL"):
                print(f"🔗 Original Embed URL: {data['embedURL'][:80]}...")
            
            # Check if we have extraction error details
            if 'extraction_error' in data:
                print(f"\n⚠️  Extraction Error Details:")
                print(f"   {data['extraction_error']}")
                print(f"\n💡 This means the enhanced extractor attempted decryption but failed.")
                print(f"   The MegaCloud protection is too sophisticated for this Python implementation.")
            
        else:
            print(f"❌ FAILED")
            error = result.get('error', 'Unknown error')
            error_type = result.get('error_type', 'Unknown')
            print(f"Error: {error}")
            print(f"Error Type: {error_type}")
            
    except Exception as e:
        print(f"❌ UNEXPECTED ERROR")
        print(f"Error: {str(e)}")
        print(f"Type: {type(e).__name__}")
        import traceback
        traceback.print_exc()

def test_direct_megacloud_url():
    """Test the MegaCloud extractor directly with the embed URL"""
    
    print(f"\n🔧 Testing Direct MegaCloud Extractor")
    print("=" * 60)
    
    # The MegaCloud URL we got from the previous test
    megacloud_url = "https://megacloud.blog/embed-2/v2/e-1/NxSK1hC8AaR5?k=1"
    
    print(f"🔗 Testing URL: {megacloud_url}")
    print()
    
    try:
        from scrapers.extractors import MegaCloudExtractor
        
        extractor = MegaCloudExtractor()
        result = extractor.extract(megacloud_url)
        
        print(f"✅ Extractor completed")
        print(f"📊 Result type: {type(result)}")
        
        sources = result.get("sources", [])
        subtitles = result.get("subtitles", [])
        
        print(f"📊 Found {len(sources)} source(s)")
        print(f"📊 Found {len(subtitles)} subtitle(s)")
        
        for i, source in enumerate(sources, 1):
            url = source.get('url', 'N/A')
            quality = source.get('quality', 'N/A')
            source_type = source.get('type', 'N/A')
            note = source.get('note', '')
            
            print(f"\n   📹 Source {i}:")
            print(f"      Type: {source_type}")
            print(f"      Quality: {quality}")
            print(f"      URL: {url[:100]}{'...' if len(url) > 100 else ''}")
            
            if note:
                print(f"      📝 Note: {note}")
            
            # Check if we got actual video URLs
            if url.endswith('.m3u8'):
                print(f"      🎯 SUCCESS! Got HLS stream URL!")
            elif url.endswith('.mp4'):
                print(f"      🎯 SUCCESS! Got MP4 video URL!")
            elif url == megacloud_url:
                print(f"      📺 Still embed URL - decryption failed")
            else:
                print(f"      ❓ Unknown result")
        
        if 'extraction_error' in result:
            print(f"\n⚠️  Extraction Error: {result['extraction_error']}")
        
    except Exception as e:
        print(f"❌ Direct extractor test failed: {str(e)}")
        import traceback
        traceback.print_exc()

def show_analysis():
    """Show analysis of the results"""
    
    print(f"\n📊 Analysis & Next Steps")
    print("=" * 60)
    
    print("🔍 What We Learned:")
    print("   ✅ The Python implementation correctly gets the MegaCloud embed URL")
    print("   ✅ The enhanced extractor attempts AES decryption")
    print("   ⚠️  MegaCloud's protection is more sophisticated than simple AES")
    print("   ⚠️  The WASM-based decryption cannot be replicated in Python easily")
    
    print("\n💡 Solutions That Will Work:")
    print("   1. 🚀 Use TypeScript aniwatch-api as microservice (RECOMMENDED)")
    print("   2. 🌐 Browser automation with Selenium/Playwright")
    print("   3. 🔄 Hybrid Python + Node.js approach")
    print("   4. 📺 Use embed URLs directly in compatible video players")
    print("   5. 🔧 Try alternative servers (hd-2, streamsb, streamtape)")
    
    print("\n🎯 Immediate Actions:")
    print("   - The current implementation provides the correct embed URL")
    print("   - Use this URL with video players that support embed playback")
    print("   - Or integrate with the TypeScript aniwatch-api for full decryption")
    
    print("\n📖 Documentation:")
    print("   - See MEGACLOUD_SOLUTION_GUIDE.md for detailed solutions")
    print("   - The Python implementation is working correctly within its limitations")

if __name__ == "__main__":
    try:
        test_enhanced_megacloud()
        test_direct_megacloud_url()
        show_analysis()
        
        print(f"\n🏁 Enhanced MegaCloud test completed!")
        
    except KeyboardInterrupt:
        print("\n\n⚠️  Test interrupted by user")
    except Exception as e:
        print(f"\n\n💥 Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
