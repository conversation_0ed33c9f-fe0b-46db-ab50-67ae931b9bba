# Anime Episode Sources MCP Tool

This document describes the implementation of the `getAnimeEpisodeSources` MCP (Model Context Protocol) tool for extracting anime episode streaming sources.

## Overview

The anime episode sources scraper is designed to extract streaming URLs from anime streaming sites, following the same architectural patterns as the existing aniwatch implementation. It supports multiple streaming servers and provides comprehensive error handling.

## Features

### Supported Servers
- **VidStreaming (hd-1)** - Primary streaming server with high quality
- **MegaCloud** - Alternative streaming server  
- **VidCloud (hd-2)** - Secondary streaming server
- **StreamSB** - StreamSB server
- **StreamTape** - StreamTape server

### Supported Categories
- **sub** - Japanese audio with subtitles
- **dub** - English dubbed audio
- **raw** - Japanese audio without subtitles

### Episode ID Formats
The scraper handles anime episode IDs in the following formats:
- `anime-name-id?ep=episode_number` (e.g., `attack-on-titan-112?ep=3304`)
- `the-children-of-shiunji-family-19556?ep=136211`

## MCP Tools

### 1. get_anime_episode_sources

Extracts streaming sources for a specific anime episode.

**Parameters:**
- `episode_id` (string, required): Episode ID in format 'anime-name-id?ep=episode_number'
- `server` (string, optional): Server type (default: "hd-1")
  - Valid values: "hd-1", "megacloud", "hd-2", "streamsb", "streamtape"
- `category` (string, optional): Episode category (default: "sub")
  - Valid values: "sub", "dub", "raw"

**Returns:**
```json
{
  "success": true,
  "data": {
    "headers": {
      "Referer": "https://example.com/"
    },
    "sources": [
      {
        "url": "https://streaming-url.com/video.m3u8",
        "quality": "720p",
        "isM3U8": true,
        "size": null
      }
    ],
    "subtitles": [
      {
        "id": "en",
        "url": "https://subtitles-url.com/en.vtt",
        "lang": "English"
      }
    ],
    "intro": {
      "start": 10,
      "end": 90
    },
    "download": null,
    "embedURL": null,
    "anilistID": 16498,
    "malID": 16498
  }
}
```

**Error Response:**
```json
{
  "success": false,
  "error": "Error message",
  "error_type": "AnimeEpisodeSourcesError",
  "status_code": 400
}
```

### 2. get_available_servers

Returns information about available streaming servers and categories.

**Parameters:** None

**Returns:**
```json
{
  "success": true,
  "servers": [
    {
      "id": "hd-1",
      "name": "VidStreaming",
      "description": "Primary streaming server with high quality"
    }
  ],
  "categories": [
    {
      "id": "sub",
      "name": "Subtitled", 
      "description": "Japanese audio with subtitles"
    }
  ]
}
```

## Implementation Details

### Architecture

The implementation follows the same patterns as the existing aniwatch TypeScript code:

1. **Data Structures**: Python dataclasses mirror the TypeScript types
2. **Server Mapping**: Maps server names to internal IDs (vidstreaming -> 4, rapidcloud -> 1, etc.)
3. **Two-Step Process**: 
   - First, get the list of available servers for the episode
   - Then, get the actual streaming URL from the selected server
4. **Extractors**: Use appropriate extractors based on the streaming server type

### File Structure

```
scrapers/
├── animeEpisodeSources.py    # Main implementation
├── extractors.py             # Video extractors for different servers
├── client.py                 # HTTP client with proper headers
├── constants.py              # Base URLs and configuration
└── __init__.py              # Package exports
```

### Key Classes

- `AnimeEpisodeSourcesError`: Custom exception for episode source errors
- `Servers`: Enum defining available streaming servers
- `ScrapedAnimeEpisodesSources`: Main data structure for episode sources
- `Video`, `Subtitle`, `Intro`: Supporting data structures

### Error Handling

The implementation includes comprehensive error handling:
- Input validation for episode IDs, servers, and categories
- Network timeout handling (10-second timeouts)
- Server availability checking
- Graceful fallbacks for missing metadata

## Usage Examples

### Basic Usage

```python
from scrapers.animeEpisodeSources import get_anime_episode_sources

# Get sources for Attack on Titan episode
result = get_anime_episode_sources(
    episode_id="attack-on-titan-112?ep=3304",
    server="hd-1",
    category="sub"
)

if result["success"]:
    sources = result["data"]["sources"]
    for source in sources:
        print(f"Quality: {source['quality']}, URL: {source['url']}")
```

### MCP Tool Usage

```python
# Using the MCP tool
result = await get_anime_episode_sources(
    ctx=context,
    episode_id="the-children-of-shiunji-family-19556?ep=136211",
    server="megacloud",
    category="sub"
)
```

### Server Information

```python
# Get available servers
servers_info = await get_available_servers(ctx=context)
for server in servers_info["servers"]:
    print(f"{server['id']}: {server['name']} - {server['description']}")
```

## Testing

The implementation includes comprehensive tests:

- **Structure Tests**: Verify imports, data structures, and basic functionality
- **Validation Tests**: Test input validation and error handling
- **MCP Integration Tests**: Verify MCP tool registration and functionality

Run tests with:
```bash
python test_structure.py      # Test basic structure
python test_mcp_tools.py      # Test MCP integration
```

## Dependencies

- `requests` - HTTP requests
- `beautifulsoup4` - HTML parsing
- `cloudscraper` - Cloudflare bypass
- `mcp` - Model Context Protocol framework

## Important Notes About MegaCloud URLs

### Why You See "We're Sorry!" Pages

The URLs like `https://megacloud.blog/embed-2/v2/e-1/NxSK1hC8AaR5?k=1` that show "We're Sorry!" are **embed URLs**, not direct video links. This is expected behavior and part of the normal extraction process.

### The Complexity Challenge

The original TypeScript implementation in the aniwatch folder uses extremely sophisticated techniques:

1. **WASM (WebAssembly) Execution**: Loads and executes binary WASM modules for decryption
2. **Browser Environment Simulation**: Creates fake window, document, and canvas objects
3. **PNG Image Processing**: Extracts decryption keys from specially encoded PNG images
4. **Multi-Layer Encryption**: Uses AES decryption, XOR operations, and custom algorithms
5. **Dynamic JavaScript Execution**: Executes and parses JavaScript code for key extraction
6. **Real-Time Anti-Bot Evasion**: Constantly evolving protection mechanisms

### Python Implementation Limitations

This Python implementation provides:
- ✅ **Correct API Structure**: Same data formats and error handling as TypeScript
- ✅ **Server Detection**: Proper server identification and routing
- ✅ **Basic Extraction**: Simple pattern-based source extraction
- ✅ **Embed URL Handling**: Returns embed URLs with proper metadata
- ❌ **Full Decryption**: Cannot replicate WASM-based decryption
- ❌ **Advanced Anti-Bot**: Limited protection bypass capabilities

### Practical Solutions

For production use, consider these approaches:

1. **Use Embed URLs Directly**: Many video players can handle embed URLs
2. **Browser Automation**: Use Selenium/Playwright to execute JavaScript
3. **Hybrid Approach**: Combine Python scraping with Node.js decryption
4. **API Integration**: Use the TypeScript aniwatch-api as a microservice

### Implementation Notes

- The implementation uses timeouts to prevent hanging requests
- Cloudflare protection is handled via cloudscraper
- The scraper respects the same server ID mappings as the TypeScript implementation
- Metadata (AniList ID, MAL ID) is extracted when available
- The implementation is designed to be robust and handle various edge cases
- Embed URLs are returned as fallbacks when direct extraction is not possible

## Integration with Existing Code

The anime episode sources functionality integrates seamlessly with the existing MCP tools:
- Follows the same error handling patterns
- Uses the same logging configuration
- Maintains consistent return formats
- Integrates with the existing client and constants
