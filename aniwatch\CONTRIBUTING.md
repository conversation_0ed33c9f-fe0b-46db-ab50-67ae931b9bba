# Contributing to aniwatch

Thank you for your interest in contributing to aniwatch. We appreciate whatever form of contribution you are willing to make. Every contribution counts ✨

## Table of Contents

- [Types of contributions we are looking for](#types-of-contributions-we-are-looking-for)
- [Ground Rules & Expectations](#ground-rules--expectations)
- [How To Contribute](#how-to-contribute)
- [Prerequisites](#prerequisites)
    - [Clone the repository](#clone-the-repository)
    - [Project Structure](#project-structure)
- [Commit Messages](#commit-messages)

## Types of contributions we are looking for

In short, we welcome any sort of contribution you are willing to make as each and every contribution counts. We gladly accept contributions such as:

- Documentation improvements: from minor typos to major document overhauls
- Helping others by answering questions in pull requests.
- Fixing known [bugs](https://github.com/ghoshRitesh12/aniwatch/issues?q=is%3Aopen).

## Ground Rules & Expectations

Before we begin, here are a few things we anticipate from you and that you should expect from others:

- Be respectful and thoughtful in your conversations around this project. Each person may have their own views and opinions about the project. Try to listen to each other and reach an agreement or compromise.

## How To Contribute

If you'd like to contribute, start by searching through the [issues](https://github.com/ghoshRitesh12/aniwatch/issues) and [pull requests](https://github.com/ghoshRitesh12/aniwatch/pulls) to see whether someone else has raised a similar idea or question.

If you don't see your idea listed, and you think it fits into the goals of this guide, you may do one of the following:

- **If your contribution is minor,** such as a typo fix or new provider, consider opening a pull request.
- **If your contribution is major,** such as a major refactor, start by opening an issue first. That way, other people can weigh in on the discussion before you do any work.

## Prerequisites

To contribute to this project, you must know the following:

- [NodeJS](https://nodejs.org/)
- [TypeScript](https://www.typescriptlang.org/)
- Web Scraping
    - [Cheerio](https://cheerio.js.org/)
    - [Axios](https://axios-http.com/docs/intro)
    - [CSS Selectors](https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Selectors)
    - [Browser Dev Tools](https://developer.mozilla.org/en-US/docs/Learn/Common_questions/Tools_and_setup/What_are_browser_developer_tools)

### Clone the repository

1. [Fork the repository](https://github.com/ghoshRitesh12/aniwatch/fork)
2. Clone your fork to your local machine using the following command (replace <your_username> with your actual GitHub username)

```bash
git clone https://github.com/<your_username>/aniwatch
```

3. Creating a new branch <br/>
   Replace \<new-branch-name> with any of the following naming conventions: <br/>
    - `feature/<branch-name>` - for adding new features
    - `bug/<branch-name>` - for fixing known bugs
    - `misc/<branch-name>` - for anything other than bug or features

```bash
git checkout -b <new-branch-name>
```

### Project Structure

- `src` directory contains all the source code required for this project

    - `types` directory contains all types & interfaces used for this project
    - `parsers` directory contains all the parsing aka scraping logic
    - `utils` directory contains handy utility methods and properties
    - `config` directory contains api configuration related files
    - `extractors` directory contains anime streaming url extractor files
      <br/><br/>

- `test` directory contains all the tests that needs to be evaluated

## Commit Messages

When you've made changes to one or more files, you have to commit that file. You also need a message for that commit.

We follow [Conventional Commit Messages](https://www.conventionalcommits.org/en/v1.0.0/#summary).

A brief overview:

- `feat`: A feature, possibly improving something already existing
- `fix`: A fix, for example of a bug
- `perf`: Performance related change
- `refactor`: Refactoring a specific section of the codebase
- `style`: Everything related to styling code like whitespaces, tabs, indenting, etc.
- `test`: Everything related to testing
- `docs`: Everything related to documentation
- `chore`: Code maintenance

Examples:

- `docs: fixed typo in readme`
- `feat: added a new category parser`
- `fix: fixed search results bug`
