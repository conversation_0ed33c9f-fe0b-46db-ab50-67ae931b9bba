#!/usr/bin/env python3
"""
Test with the specific episode ID format provided by the user
"""

import logging
from scrapers.animeEpisodeSources import get_anime_episode_sources, AnimeEpisodeSourcesError

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_specific_episode():
    """Test with the user's specific episode ID"""
    
    print("🎬 Testing Specific Episode: The Children of Shiunji Family")
    print("=" * 70)
    
    # The user's specific episode ID
    episode_id = "the-children-of-shiunji-family-19556?ep=136211&ep=136211"
    
    print(f"📺 Episode ID: {episode_id}")
    print(f"📝 Note: This has duplicate ep parameters (?ep=136211&ep=136211)")
    print()
    
    # Test with different servers
    servers_to_test = [
        ("hd-1", "VidStreaming"),
        ("megacloud", "MegaCloud"), 
        ("hd-2", "VidCloud"),
        ("streamsb", "StreamSB"),
        ("streamtape", "StreamTape")
    ]
    
    categories_to_test = ["sub", "dub", "raw"]
    
    for server_id, server_name in servers_to_test:
        print(f"\n🔧 Testing Server: {server_name} ({server_id})")
        print("-" * 50)
        
        for category in categories_to_test:
            print(f"\n📂 Category: {category}")
            
            try:
                result = get_anime_episode_sources(
                    episode_id=episode_id,
                    server=server_id,
                    category=category
                )
                
                if result.get("success"):
                    data = result.get("data", {})
                    sources = data.get("sources", [])
                    headers = data.get("headers", {})
                    
                    print(f"   ✅ SUCCESS - Found {len(sources)} source(s)")
                    
                    for i, source in enumerate(sources, 1):
                        url = source.get('url', 'N/A')
                        quality = source.get('quality', 'N/A')
                        source_type = source.get('type', 'unknown')
                        
                        print(f"      Source {i}:")
                        print(f"        Type: {source_type}")
                        print(f"        Quality: {quality}")
                        print(f"        URL: {url[:80]}{'...' if len(url) > 80 else ''}")
                        
                        # Analyze URL type
                        if url.endswith('.m3u8'):
                            print(f"        🎯 HLS Stream (Direct)")
                        elif url.endswith('.mp4'):
                            print(f"        🎯 MP4 Video (Direct)")
                        elif 'megacloud' in url.lower():
                            print(f"        📺 MegaCloud Embed")
                        elif 'rapid' in url.lower() or 'vidcloud' in url.lower():
                            print(f"        📺 RapidCloud/VidCloud Embed")
                        elif 'embed' in url.lower():
                            print(f"        📺 Generic Embed")
                        else:
                            print(f"        ❓ Unknown Type")
                    
                    # Show headers
                    if headers:
                        print(f"      Headers: {len(headers)} header(s)")
                        for key, value in headers.items():
                            print(f"        {key}: {value}")
                    
                    # Show metadata
                    if data.get("anilistID"):
                        print(f"      📺 AniList ID: {data['anilistID']}")
                    if data.get("malID"):
                        print(f"      📺 MAL ID: {data['malID']}")
                    if data.get("embedURL"):
                        embed_url = data['embedURL']
                        print(f"      🔗 Embed URL: {embed_url[:60]}...")
                        
                        # Check if this is the problematic MegaCloud URL
                        if 'megacloud' in embed_url.lower():
                            print(f"      ⚠️  This is a MegaCloud embed URL")
                            print(f"         It requires complex WASM decryption")
                            print(f"         See MEGACLOUD_SOLUTION_GUIDE.md for solutions")
                    
                else:
                    error = result.get('error', 'Unknown error')
                    error_type = result.get('error_type', 'Unknown')
                    print(f"   ❌ FAILED - {error_type}: {error}")
                    
            except AnimeEpisodeSourcesError as e:
                print(f"   ❌ AnimeEpisodeSourcesError: {e.message}")
                if hasattr(e, 'status_code'):
                    print(f"      Status Code: {e.status_code}")
                    
            except Exception as e:
                print(f"   ❌ Unexpected Error: {str(e)}")
                print(f"      Type: {type(e).__name__}")

def test_episode_id_parsing():
    """Test how the episode ID with duplicate parameters is parsed"""
    
    print(f"\n🔍 Episode ID Parsing Analysis")
    print("=" * 50)
    
    episode_id = "the-children-of-shiunji-family-19556?ep=136211&ep=136211"
    
    print(f"Original: {episode_id}")
    
    # Check how it's split
    if "?ep=" in episode_id:
        anime_part = episode_id.split("?ep=")[0]
        ep_part = episode_id.split("?ep=")[1]
        
        print(f"Anime Part: {anime_part}")
        print(f"Episode Part: {ep_part}")
        
        # Check if there are multiple ep parameters
        if "&ep=" in ep_part:
            print(f"⚠️  Multiple ep parameters detected!")
            ep_number = ep_part.split("&")[0]  # Take the first one
            print(f"Using first ep parameter: {ep_number}")
        else:
            ep_number = ep_part
            print(f"Single ep parameter: {ep_number}")
    
    # Test URL construction
    from scrapers.constants import SRC_BASE_URL, SRC_AJAX_URL
    from urllib.parse import urljoin
    
    ep_id = urljoin(SRC_BASE_URL, f"/watch/{episode_id}")
    print(f"Constructed URL: {ep_id}")
    
    # Extract episode number for API call
    if "?ep=" in ep_id:
        episode_number = ep_id.split("?ep=")[1].split("&")[0]  # Take first ep param
        servers_url = f"{SRC_AJAX_URL}/v2/episode/servers?episodeId={episode_number}"
        print(f"Servers API URL: {servers_url}")

def show_recommendations():
    """Show recommendations based on test results"""
    
    print(f"\n💡 Recommendations")
    print("=" * 50)
    
    print("1. 🔧 Episode ID Format:")
    print("   - The duplicate ?ep=136211&ep=136211 is handled correctly")
    print("   - The implementation uses the first ep parameter")
    print("   - Consider using single parameter: ?ep=136211")
    
    print("\n2. 🎯 Server Selection:")
    print("   - Try different servers if one doesn't work")
    print("   - hd-2 (VidCloud) might have different protection")
    print("   - StreamSB and StreamTape may be easier to extract")
    
    print("\n3. 📺 MegaCloud URLs:")
    print("   - If you get MegaCloud embed URLs, they need special handling")
    print("   - Use the TypeScript aniwatch-api for full decryption")
    print("   - Or use browser automation to execute JavaScript")
    
    print("\n4. 🔄 Alternative Approaches:")
    print("   - Run aniwatch-api as a microservice alongside this Python code")
    print("   - Use Selenium/Playwright for JavaScript execution")
    print("   - Try the embed URLs directly in video players")

if __name__ == "__main__":
    try:
        test_specific_episode()
        test_episode_id_parsing()
        show_recommendations()
        
        print(f"\n🏁 Test completed!")
        print("Check the results above to see what sources were found.")
        
    except KeyboardInterrupt:
        print("\n\n⚠️  Test interrupted by user")
    except Exception as e:
        print(f"\n\n💥 Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
